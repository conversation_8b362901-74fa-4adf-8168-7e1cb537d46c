package com.xiaodu.serialportdemo.mqtt;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xiaodu.serialportdemo.constant.SerialPortConstant;
import com.xiaodu.serialportdemo.service.CarService;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.Map;


@Configuration
public class MQTTConfig {
    private static final Logger log = LoggerFactory.getLogger(MQTTConfig.class);

    @Autowired
    @Lazy
    private CarService carService;

    public JSONObject getMapData (){
        JSONObject projectId = carService.getProjectId();
        return projectId;
    }
    @Bean("mqttClientOne")
    public JwMqttClient jwMqttClient() {
        Map<String, String> map = carService.getToken();
        String token = map.get("token");
        String timestamp = map.get("timestamp");
        String oldClientId = getMapData().getJSONObject("client").getString("id");
        String clientId = SerialPortConstant.MQTT_CONF.MQTT_CLIENT_ID_PREFIX + oldClientId;
        int timeOut = 10;
        int keepAlive = 20;
        JwMqttClient jwMqttClient = new JwMqttClient(SerialPortConstant.MQTT_CONF.HUAWEI_MQTT_HOST, timestamp, token, clientId, timeOut, keepAlive);
        for (int i = 0; i < 10; i++) {
            try {
                jwMqttClient.connect();
                //不同的主题

                jwMqttClient.subscribe(MqttConstants.TAKE_OFF + "_" + oldClientId, 2);
                return jwMqttClient;
            } catch (MqttException e) {
                log.error("MQTT1连接异常,重新连接次数"+i);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return jwMqttClient;
    }

    @Bean("mqttClientTwo")
    public JwMqttClient jwMqttClientTwo() {
        JSONObject mapData = getMapData();
        String YTJClientId = mapData.getJSONObject("client").getString("id");
        JSONArray cameras = mapData.getJSONArray("cameras");
        String cameraSN = null;

        if (cameras != null) {
            for (int i = 0; i < cameras.size(); i++) {
                JSONObject camera = cameras.getJSONObject(i);
                String ip = camera.getString("ip");
                if (StringUtils.isNotBlank(ip) && ip.endsWith("2")) {
                    cameraSN = camera.getString("sn");
                    break;
                }
            }
        }

        String username = "admin";
        String password = "admin123";
        String clientId = "excavation";
        int timeOut = 10;
        int keepAlive = 20;
        JwMqttClient jwMqttClient = new JwMqttClient(SerialPortConstant.MQTT_CONF.LOCAL_MQTT_HOST, username, password, clientId, timeOut, keepAlive);
        for (int i = 0; i < 10; i++) {
            try {
                jwMqttClient.connectTwo();
                //不同的主题
                jwMqttClient.subscribe(MqttConstants.TAKE_OFF+"_"+YTJClientId,2);
                jwMqttClient.subscribe(MqttConstants.REPLY_FRONT+cameraSN+MqttConstants.REPLY_LAST,2);
                return jwMqttClient;
            } catch (MqttException e) {
                log.error("MQTT2连接异常,重新连接次数"+i);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return jwMqttClient;
    }

    /**
     * 绑定回调到 mqttClientOne
     */
    @Bean
    public JwMqttCallback mqttCallback(JwMqttClient mqttClientOne, JwMqttClient mqttClientTwo) throws MqttException {
        JwMqttCallback callback = new JwMqttCallback(mqttClientOne, mqttClientTwo);
        mqttClientOne.getClient().setCallback(callback);
        mqttClientOne.subscribe("topic/one", 1);
        return callback;
    }

    @Bean
    public JwMqttCallback mqttCallbackTwo(JwMqttClient mqttClientTwo) throws MqttException {
        JwMqttCallback callback = new JwMqttCallback(mqttClientTwo,mqttClientTwo);
        mqttClientTwo.getClient().setCallback(callback);
        mqttClientTwo.subscribe("topic/two", 1);
        return callback;
    }
}

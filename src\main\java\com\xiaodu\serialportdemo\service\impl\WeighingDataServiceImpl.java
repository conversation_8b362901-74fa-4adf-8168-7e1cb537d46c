package com.xiaodu.serialportdemo.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xiaodu.serialportdemo.mapper.WeighingDataMapper;
import com.xiaodu.serialportdemo.service.WeighingDataService;
import com.xiaodu.serialportdemo.vo.WeighingDataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName: WeighingDataServiceImpl
 * Package: com.xiaodu.serialportdemo.service.impl
 * Description:
 *
 * @Create 2025-01-18 18:07
 */
@Service
public class WeighingDataServiceImpl implements WeighingDataService {

    @Autowired
    @Lazy
    private WeighingDataMapper mapper;

    /**
     * @param carNumber
     * @return
     */
    @Override
    public Map<String, Object> getDataByCarNumber(Integer pageNumber, Integer pageSize, String carNumber,String startTime,String endTime) {
        Map<String, Object> map = new HashMap<>();
//        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1));
//        Date endTime = DateUtil.endOfDay(new Date());
        Integer offset = (pageNumber - 1) * pageSize;
        if (StringUtils.isNotBlank(carNumber)) {
            List<WeighingDataVo> dataByCarNumber = mapper.getDataByCarNumber(offset, pageSize, startTime, endTime, carNumber);
            int total = mapper.getTotalCountByCarNumber(startTime, endTime, carNumber);
            map.put("total", total);
            map.put("data", dataByCarNumber);
            return map;
        } else {
            List<WeighingDataVo> dataByCreateTime = mapper.getDataByCreateTime(offset, pageSize, startTime, endTime);
            int total = mapper.getTotalCountByCreateTime(startTime, endTime);
            map.put("total", total);
            map.put("data", dataByCreateTime);
            return map;
        }
    }

    @Override
    public WeighingDataVo getImageById(String id) {
        if(StringUtils.isNotBlank(id)){
            return mapper.getImageById(id);
        }
        return null;
    }
}

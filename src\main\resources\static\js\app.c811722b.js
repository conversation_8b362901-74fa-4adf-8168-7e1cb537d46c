(function(){var e={1582:function(e,t,i){"use strict";var s=i(6848),o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},l=[],a={name:"App"},n=a,r=i(845),c=(0,r.A)(n,o,l,!1,null,null,null),h=c.exports,d=i(6178),u=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("el-container",{staticStyle:{overflow:"hidden"}},[t("el-header",{staticStyle:{"text-shadow":"0 10px 10px rgba(0, 0, 0, 0.2)",color:"#fff","font-size":"40px","font-weight":"bold","font-style":"italic"}},[e._v("河北省采(弃)砂项目")]),t("el-container",[t("el-aside",{staticStyle:{overflow:"hidden",height:"100%"},attrs:{width:"600px"}},[t("el-card",{staticClass:"box-card1"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"刷新页面",placement:"bottom-end"}},[t("el-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"success",icon:"el-icon-refresh-right",circle:"",size:"mini"},on:{click:e.SX}})],1),t("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"触发抬杆",placement:"bottom-end"}},[t("el-button",{staticStyle:{"margin-right":"20px"},attrs:{type:"warning",icon:"el-icon-smoking",circle:"",size:"mini"},on:{click:e.TG}})],1),t("span",{staticStyle:{"text-align":"left","font-weight":"900","font-size":"18px"}},[e._v("实时监控")])],1),t("div",{staticStyle:{width:"550px",height:"35vh",margin:"0 auto","margin-bottom":"50px","margin-top":"20px",position:"relative","background-image":"url(./assets/beijingtu.png)"}},[t("video",{ref:"videoElement",attrs:{width:"100%",height:"100%",id:"videoEl"}}),t("div",{staticStyle:{position:"absolute",bottom:"0",left:"0","z-index":"999"}},[0==e.buttonStatus?t("el-button",{staticStyle:{"margin-left":"10px","margin-top":"15px"},attrs:{icon:"el-icon-video-play",type:"info",size:"mini",circle:""},on:{click:e.flv_start}}):e._e(),1==e.buttonStatus?t("el-button",{staticStyle:{"margin-left":"10px","margin-top":"15px",position:"relative","z-index":"99999"},attrs:{icon:"el-icon-video-pause",size:"mini",circle:"",type:"success"},on:{click:e.flv_end}}):e._e(),t("el-button",{staticStyle:{left:"0",position:"relative","z-index":"999999"},attrs:{icon:"el-icon-full-screen",size:"mini",circle:""},on:{click:e.flv_full}})],1)]),t("div",{staticStyle:{width:"550px",height:"35vh",margin:"0 auto","margin-bottom":"70px","margin-top":"20px",position:"relative","background-image":"url(../assets/beijingtu.png)"}},[t("video",{ref:"videoElement2",attrs:{width:"100%",height:"100%",id:"videoE2"}}),t("div",{staticStyle:{position:"absolute",bottom:"0",left:"0","z-index":"999"}},[0==e.buttonStatus2?t("el-button",{staticStyle:{"margin-left":"10px","margin-top":"15px"},attrs:{icon:"el-icon-video-play",type:"info",size:"mini",circle:""},on:{click:e.flv_start2}}):e._e(),1==e.buttonStatus2?t("el-button",{staticStyle:{"margin-left":"10px","margin-top":"15px",position:"relative","z-index":"99999"},attrs:{icon:"el-icon-video-pause",size:"mini",circle:"",type:"success"},on:{click:e.flv_end2}}):e._e(),t("el-button",{staticStyle:{left:"0",position:"relative","z-index":"999999"},attrs:{icon:"el-icon-full-screen",size:"mini",circle:""},on:{click:e.flv_full2}})],1)])])],1),t("el-container",[t("el-main",{staticStyle:{overflow:"hidden"}},[t("el-card",{staticClass:"box-card",staticStyle:{height:"84vh"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",{staticStyle:{"text-align":"left","font-weight":"900","font-size":"18px"}},[e._v("车辆信息-"),t("span",{class:0==e.carStatus?"carNoBoxInputTrue":"carNoBoxInputFalse"},[e._v(e._s(0==e.carStatus?"出场":"入场"))])]),t("el-button",{staticStyle:{float:"right"},attrs:{type:"success",size:"small ",round:""},on:{click:e.showHistory}},[e._v("历史数据")])],1),t("el-descriptions",{attrs:{title:"信息展示",column:1}},[t("el-descriptions-item",{attrs:{label:"项目名称"}},[t("el-tag",{attrs:{size:"medium"}},[e._v(e._s(e.ClientData.client.projectName))])],1),t("el-descriptions-item",{attrs:{label:"磅站名称"}},[t("el-tag",{attrs:{size:"medium"}},[e._v(e._s(e.ClientData.client.stationName))])],1),t("el-descriptions-item",{attrs:{label:"车牌号"}},[t("div",{staticStyle:{"text-align":"center"}},[t("div",{class:e.licensePlateUnit.length<=7?"carNoBoxInput":"carNoBoxInput1"},[t("div",{staticStyle:{padding:"6px",border:"2px solid #fff","border-radius":"6px",margin:"6px 3px 6px 6px"}},[t("input",{ref:"inputBox0",staticClass:"inputBox",domProps:{value:e.licensePlateUnit[0]},on:{click:function(t){e.licensePlateDoor=!0},change:e.handleSelect}}),t("input",{staticClass:"inputBox",domProps:{value:e.licensePlateUnit[1]},on:{click:function(t){e.licensePlateDoor=!0},change:e.license1}}),t("span",{staticClass:"dot"}),t("input",{staticClass:"inputBox",domProps:{value:e.licensePlateUnit[2]},on:{click:function(t){e.licensePlateDoor=!0},change:e.license2}}),t("input",{staticClass:"inputBox",domProps:{value:e.licensePlateUnit[3]},on:{click:function(t){e.licensePlateDoor=!0},change:e.license3}}),t("input",{staticClass:"inputBox",domProps:{value:e.licensePlateUnit[4]},on:{click:function(t){e.licensePlateDoor=!0},change:e.license4}}),t("input",{staticClass:"inputBox",domProps:{value:e.licensePlateUnit[5]},on:{click:function(t){e.licensePlateDoor=!0},change:e.license5}}),t("input",{staticClass:"inputBox",domProps:{value:e.licensePlateUnit[6]},on:{click:function(t){e.licensePlateDoor=!0},change:e.license6}}),7===e.licensePlateUnit.length-1?t("input",{staticClass:"inputBox",class:7===e.licensePlateUnit.length-1?"inputBoxActive":"inputBox",domProps:{value:e.licensePlateUnit[7]},on:{change:e.license7}}):e._e(),7!==e.licensePlateUnit.length-1?t("img",{staticStyle:{height:"34px",width:"34px"},attrs:{src:i(8667),alt:"新能源"}}):e._e()])]),1==e.licensePlateDoor?t("ul",{staticStyle:{"background-color":"red",display:"flex",width:"600px",height:"36px","line-height":"36px",background:"#d0d5d9",position:"absolute",left:"630px","z-index":"4"}},e._l(e.firstSixItems,(function(i){return t("li",{key:i,staticStyle:{width:"100px","list-style-type":"none",height:"36px","line-height":"36px",color:"#fff","font-weight":"bold","text-align":"center"},on:{click:function(t){return e.handleClick(i)}}},[e._v(" "+e._s(i)+" ")])})),0):e._e(),1==e.licensePlateDoor?t("div",{staticStyle:{position:"absolute",top:"410px",left:"630px",width:"100%","z-index":"999"}},[e.licensePlateUnitLength<=0?t("div",{staticClass:"carNoBox"},[e._l(e.columns,(function(i){return t("span",{key:i,staticClass:"carNo",on:{click:function(t){return e.pickOn(i)}}},[e._v(" "+e._s(i)+" ")])})),t("span",{staticClass:"delBt",on:{click:e.delCarNo}},[e._v("X")])],2):e._e(),e.licensePlateUnitLength>=1?t("div",{staticClass:"carNoBox"},[e._l(e.numberColumns,(function(i){return t("span",{key:i,staticClass:"carNo",on:{click:function(t){return e.pickOn(i)}}},[e._v(" "+e._s(i)+" ")])})),t("div",{staticStyle:{display:"flex","align-items":"center","text-align":"center"}},[t("span",{staticClass:"delBt",staticStyle:{"text-align":"center"},on:{click:e.delCarNo}},[e._v("X")]),t("span",{staticClass:"delBt",staticStyle:{"margin-left":"6px",width:"42px","background-color":"#67c23a",color:"#fff"},on:{click:e.confirm}},[e._v("确认")]),t("span",{staticClass:"delBt",staticStyle:{"margin-left":"6px",width:"42px","background-color":"red",color:"#fff"},on:{click:e.close}},[e._v("关闭")])])],2):e._e()]):e._e()])]),t("el-descriptions-item",{staticStyle:{display:"flex","align-items":"center"},attrs:{label:"载重(吨)"}},[t("el-tag",{style:[e.colorStyle],attrs:{size:"medium"}},[e._v(e._s(e.weight)+" ")]),t("span",{staticStyle:{"font-size":"14px",color:"red","line-height":"36px",display:"block"}},[e._v(e._s(e.endData))])],1),t("el-descriptions-item",{attrs:{label:"类型"}},[t("el-radio-group",{attrs:{size:"small"},on:{input:e.handleChangeType},model:{value:e.carStatus,callback:function(t){e.carStatus=t},expression:"carStatus"}},[t("el-radio-button",{attrs:{label:!0}},[e._v("入场")]),t("el-radio-button",{staticStyle:{"margin-left":"35px"},attrs:{label:!1}},[e._v("出场")])],1)],1),t("el-descriptions-item",{attrs:{label:"抓拍照片"}},[t("div",{staticStyle:{width:"500px",height:"40vh",margin:"0 auto"}},[e.leftUrl?t("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:e.leftUrl,"preview-src-list":[e.leftUrl]}}):t("img",{attrs:{src:i(2185),width:"100%",height:"100%",alt:""}})],1),t("div",{staticStyle:{width:"500px",height:"40vh","background-color":"red",margin:"0 auto","margin-left":"20px"}},[e.rightUrl?t("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:e.rightUrl,"preview-src-list":[e.rightUrl]}}):t("img",{attrs:{src:i(2185),width:"100%",height:"100%",alt:""}})],1)])],1)],1)],1),t("el-footer",{staticStyle:{"background-color":"#e9eef3",padding:"0px 20px"}},[t("div",{staticClass:"footerCard"},[t("el-button",{attrs:{type:0==e.updateState?"success":"danger"},on:{click:e.updateButton}},[e._v(e._s(e.updateText))]),t("el-button",{on:{click:e.replayget}},[e._v("重新获取")]),t("el-button",{attrs:{type:"primary",disabled:e.submitLoading},on:{click:e.submitData}},[e._v("提交")])],1)])],1)],1)],1),t("el-dialog",{attrs:{title:"历史数据",visible:e.dialogTableVisible,width:"1200px"},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[t("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.formInline},nativeOn:{submit:function(e){e.preventDefault()}}},[t("el-form-item",{attrs:{label:"车牌号"}},[t("el-input",{attrs:{placeholder:"请输入车牌号",size:"small"},on:{input:function(t){return e.getHistory(1)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.getHistory(1)}},model:{value:e.formInline.cardNumber,callback:function(t){e.$set(e.formInline,"cardNumber",t)},expression:"formInline.cardNumber"}})],1),t("el-form-item",{attrs:{label:"过磅时间"}},[t("el-date-picker",{attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","picker-options":e.pickerOptions,"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",align:"right","default-time":["00:00:00","23:59:59"]},on:{change:e.searchTime},model:{value:e.guobengTime,callback:function(t){e.guobengTime=t},expression:"guobengTime"}})],1)],1),e.tableState?t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.gridData,height:"55vh","element-loading-text":"数据过多，拼命加载中..."}},[t("el-table-column",{attrs:{property:"carNuber",label:"车牌号",width:"100"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",{staticStyle:{color:"#67c23a","font-weight":"800"}},[e._v(e._s(i.row.carNumber))])]}}],null,!1,3420957114)}),t("el-table-column",{attrs:{property:"createTime",label:"过磅时间","min-width":"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("span",{staticStyle:{color:"#409eff","font-weight":"800"}},[e._v(e._s(i.row.createTime))])]}}],null,!1,1112449346)}),t("el-table-column",{attrs:{property:"weight",label:"载重量(吨)"},scopedSlots:e._u([{key:"default",fn:function(i){return[i.row.weight<10?t("span",{staticStyle:{color:"#e6a23c","font-weight":"800"}},[e._v(e._s(i.row.weight.toFixed(3)))]):i.row.weight>=40?t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v(e._s(i.row.weight.toFixed(3)))]):t("span",{staticStyle:{color:"#67c23a","font-weight":"800"}},[e._v(e._s(i.row.weight))])]}}],null,!1,2057303411)}),t("el-table-column",{attrs:{property:"dataType",label:"类型"},scopedSlots:e._u([{key:"default",fn:function(i){return["in"==i.row.dataType?t("el-tag",{attrs:{effect:"dark",type:"success"}},[e._v(" "+e._s("入场")+" ")]):e._e(),"out"==i.row.dataType?t("el-tag",{attrs:{effect:"dark",type:"danger"}},[e._v(" "+e._s("出场")+" ")]):e._e()]}}],null,!1,3513886106)}),t("el-table-column",{attrs:{property:"isPull",label:"数据状态"},scopedSlots:e._u([{key:"default",fn:function(i){return["0"==i.row.isPull?t("el-tag",{attrs:{effect:"dark",type:"danger"}},[e._v(" "+e._s("未上传")+" ")]):e._e(),"1"==i.row.isPull?t("el-tag",{attrs:{effect:"dark",type:"success"}},[e._v(" "+e._s("已上传")+" ")]):e._e()]}}],null,!1,9832437)}),t("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-button",{attrs:{type:"primary",disabled:e.phoneLoading},on:{click:function(t){return e.handleEdit(i.$index,i.row)}}},[e._v("查看抓拍照片")])]}}],null,!1,1644244456)})],1):e._e(),e.pageData.total>0?t("el-pagination",{attrs:{"current-page":e.pageData.pageNumber,"page-sizes":[10,20,50,100],"page-size":10,layout:"total, sizes, prev, pager, next, jumper",total:e.pageData.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}}):e._e()],1),t("el-dialog",{attrs:{title:"车牌识别",visible:e.dialogCarVisible,width:"30%","before-close":e.handleClose,"show-close":e.showClose},on:{"update:visible":function(t){e.dialogCarVisible=t}}},[t("span",[e._v("检测到车牌"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v(e._s(e.showCar))]),e._v("疑似伪造信息是否获取识别结果")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){return e.carTrue("清空")}}},[e._v("清空")]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.carTrue("获取")}}},[e._v("获取")])],1)]),t("el-dialog",{attrs:{title:"车牌录入情况",visible:e.dialogCarIsVisible,"show-close":e.showClose,width:"30%"},on:{"update:visible":function(t){e.dialogCarIsVisible=t}}},[t("span",[e._v("检测到车牌"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v(e._s(e.showCar))]),e._v("未录入系统")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.CarisFalse}},[e._v("确定")])],1)]),t("el-dialog",{attrs:{title:"提示",visible:e.dialogVisibleOne,width:"50%","before-close":e.handleCloseOne},on:{"update:visible":function(t){e.dialogVisibleOne=t}}},[t("span",[e._v("系统根据车辆载重检测出本次提交数据可能是"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v("出场数据")]),e._v(",请确认提交数据是否准确!")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisibleOne=!1}}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{title:"提示",visible:e.dialogVisibleTwe,width:"50%","before-close":e.handleCloseTwe},on:{"update:visible":function(t){e.dialogVisibleTwe=t}}},[t("span",[e._v("系统根据车辆载重检测出本次提交数据可能是"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v("出场数据")]),e._v(",请"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v("再次")]),e._v("确认提交数据是否准确!!!")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisibleTwe=!1}}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{title:"提示",visible:e.dialogVisibleThree,width:"50%","before-close":e.handleCloseOne},on:{"update:visible":function(t){e.dialogVisibleThree=t}}},[t("span",[e._v("系统根据车辆载重检测出本次提交数据可能是"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v("入场数据")]),e._v(",请确认提交数据是否准确!")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisibleThree=!1}}},[e._v("确 定")])],1)]),t("el-dialog",{attrs:{title:"提示",visible:e.dialogVisibleFore,width:"50%","before-close":e.handleCloseTwe},on:{"update:visible":function(t){e.dialogVisibleFore=t}}},[t("span",[e._v("系统根据车辆载重检测出本次提交数据可能是"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v("入场数据")]),e._v(",请"),t("span",{staticStyle:{color:"red","font-weight":"800"}},[e._v("再次")]),e._v("确认提交数据是否准确!!!")]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisibleFore=!1}}},[e._v("确 定")])],1)]),e.centerDialogVisible?t("el-dialog",{attrs:{title:"抓拍照片详情内容",visible:e.centerDialogVisible,width:"80%",top:"5vh","custom-class":"image-dialog"},on:{"update:visible":function(t){e.centerDialogVisible=t}}},[t("div",{staticClass:"image-container"},[t("div",{staticClass:"image-wrapper"},[t("el-image",{staticStyle:{width:"100%",height:"500px"},attrs:{src:e.urlimagL,"preview-src-list":e.srcList}},[t("div",{staticClass:"image-slot"},[e._v("加载中"),t("span",{staticClass:"dota"},[e._v("...")])])]),t("div",{staticClass:"image-caption"},[e._v("抓拍照片-1")])],1),t("div",{staticClass:"image-wrapper"},[t("el-image",{staticStyle:{width:"100%",height:"500px"},attrs:{src:e.urlimagR,"preview-src-list":e.srcList}},[t("div",{staticClass:"image-slot"},[e._v("加载中"),t("span",{staticClass:"dota"},[e._v("...")])])]),t("div",{staticClass:"image-caption"},[e._v("抓拍照片-2")])],1)]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.centerDialogVisible=!1}}},[e._v("关闭")])],1),t("div",{staticClass:"demo-image__preview"})]):e._e()],1)},g=[],p=(i(4114),i(6573),i(8100),i(7936),i(7467),i(4732),i(9577),i(8992),i(4520),i(1454),i(7550),i(4979),i(1576)),m=i.n(p),f=i(4373),v=i(1128),b=i.n(v),y=i(4430),_=i(3169),S=i.n(_);const w=S().enc.Utf8.parse("1234567890123456");var T={name:"App",components:{},data(){const e=new Date,t=new Date(e.setHours(0,0,0,0)),i=new Date(e.setHours(23,59,59,999)),s=m()(t).format("YYYY-MM-DD HH:mm:ss"),o=m()(i).format("YYYY-MM-DD HH:mm:ss");return{tgid:"",tgId:"",cremarsId:[],colorStyle:{fontSize:"30px"},weightErrorInfoNum:0,endDatas:"",phoneLoading:!1,urlimagL:null,urlimagR:null,srcList:[],centerDialogVisible:!1,ClientData:{client:{projectName:"前端测试",stationName:"前端测试"}},loading:!1,guobengTime:[s,o],pickerOptions:{shortcuts:[{text:"最近一周",onClick(e){const t=new Date,i=new Date;i.setTime(i.getTime()-6048e5),e.$emit("pick",[i,t])}},{text:"最近一个月",onClick(e){const t=new Date,i=new Date;i.setTime(i.getTime()-2592e6),e.$emit("pick",[i,t])}},{text:"最近三个月",onClick(e){const t=new Date,i=new Date;i.setTime(i.getTime()-7776e6),e.$emit("pick",[i,t])}}]},client3:null,client2:null,client:null,pageData:{pageNumber:1,pageSize:10,total:0},showClose:!1,stationId:"",weightInfo:[],weight:0,showCar:"",carIs:!1,requestToken:"",reader:null,port:null,writer:null,receivedData:"",errorMessage:"",payload:null,dialogCarVisible:!1,dialogCarIsVisible:!1,shebei1Status:!1,shebei2Status:!1,xintiaobao:"",xintiaobao2:"",submitLoading:!1,videoEl:null,videoE2:null,loading:!0,carNum:"",carNumPassword:"",carStatus:!0,tableState:!1,buttonStatus:!1,leftUrl:"",rightUrl:"",updateText:"开始采集",updateState:!1,buttonStatus2:!1,flvPlayer:null,flvPlayer2:null,equimentId1:"61d41d31-6aa5984e",equimentId2:"3c294c0b-19b7fb36",equimentId3:"3c294c0b-19b7fb32",baseUrl:"http://127.0.0.1:8077",mqttIp:"127.0.0.1",licensePlate:"",gridData:[],formInline:{cardNumber:"",startTime:s,endTime:o},searchResults:["京A12345","京A12346","京A12347","京A12348","京A12349","京A12350"],dialogTableVisible:!1,licensePlateDoor:!1,activeIndex:0,licensePlateUnit:[],columns:["京","沪","鄂","湘","川","渝","粤","闽","晋","黑","津","浙","豫","赣","贵","青","琼","宁","吉","蒙","冀","苏","皖","桂","云","陕","甘","藏","新","辽","鲁"],numberColumns:["1","2","3","4","5","6","7","8","9","0","Q","W","E","R","T","Y","U","I","O","P","A","S","D","F","G","H","J","K","L","Z","X","C","V","B","N","M","港","澳","学","领","警"],initData:"",carValue:"",licensePlateUnitLength:0,commitCishu:0,dialogVisibleOne:!1,dialogVisibleTwe:!1,dialogVisibleThree:!1,dialogVisibleFore:!1,zaizhongNum:5,timer:null,isRuning:!1,taigancarNumber:"",daozhaValue:[]}},created(){window.addEventListener("online",(()=>{this.connectMqttBroker(),this.connectMqttBroker2(),this.connectMqttBroker3(),console.info("网络恢复，重新连接 MQTT")})),this.getClient(),this.getDun(),navigator,setInterval((()=>{var e=Date.now();this.shebei1Status=e-this.xintiaobao>3e4,this.shebei2Status=e-this.xintiaobao2>3e4}),3e4),this.getmuhuSearch()},beforeDestroy(){this.flvPlayer&&(this.flvPlayer.pause(),this.flvPlayer.unload(),this.flvPlayer.detachMediaElement(),this.flvPlayer.destroy()),this.flvPlayer2&&(this.flvPlayer2.pause(),this.flvPlayer2.unload(),this.flvPlayer2.detachMediaElement(),this.flvPlayer2.destroy())},watch:{carNum(e,t){if(e&&0==this.carStatus){let t={clientId:this.ClientData.client.id,stationId:this.ClientData.client.stationId,carNumber:this.carNum};f.A.post(this.baseUrl+"/car/checkWeight",t).then((e=>{console.log(e.data.data,"我是在监听中拿的正常的"),"未申诉"!=e.data.data&&this.$message.success(e.data.data)})).catch((e=>{console.log(e,"我是在监听中拿的错误的"),this.$message.error(e)})),console.log("newValue,我是拿到的车牌号",e)}},carStatus(e,t){if(e)return;let i={clientId:this.ClientData.client.id,stationId:this.ClientData.client.stationId,carNumber:this.carNum};this.carNum&&f.A.post(this.baseUrl+"/car/checkWeight",i).then((e=>{console.log(e.data.data,"我是在监听中拿的正常的"),"未申诉"!=e.data.data&&this.$message.success(e.data.data)})).catch((e=>{console.log(e,"我是在监听中拿的错误的"),this.$message.error(e)}))},licensePlateDoor(e,t){this.getmuhuSearch(""),e&&(this.updateState=!0,this.updateButton(),console.log("我现在是输入状态"),this.client.unsubscribe("device/"+this.equimentId1+"/message/up/ivs_result",{qos:0},((e,t)=>{e||console.log("已经取消订阅自动获取数据接口")})),this.client2.unsubscribe("device/"+this.equimentId2+"/message/up/ivs_result",{qos:0},((e,t)=>{})),this.client3.unsubscribe("device/"+this.equimentId3+"/message/up/ivs_result",{qos:0},((e,t)=>{})))}},computed:{firstSixItems(){return this.searchResults.slice(0,6)}},methods:{async handleEdit(e,t){try{this.phoneLoading=!0;const e=await f.A.get(`${this.baseUrl}/car/getImageById`,{params:{id:t.id}}),{headImage:i,rearImage:s}=e.data.data;this.urlimagL=i,this.urlimagR=s,this.srcList=[i,s],this.centerDialogVisible=!0}catch(i){console.error("获取图片失败:",i),this.$message.error("获取图片失败")}finally{this.phoneLoading=!1}},searchTime(e){if(null==e)return this.formInline.startTime=null,this.formInline.endTime=null,this.getHistory();console.log(this.guobengTime,"我是时间"),this.formInline.startTime=this.guobengTime[0],this.formInline.endTime=this.guobengTime[1],this.pageData.pageNumber=1,this.getHistory()},handleChangeType(e){console.log(this.carStatus),console.log("我点击了切换类型")},fullscreenBig(){document.documentElement.requestFullscreen(),document.documentElement.webkitRequestFullscreen(),document.documentElement.mozRequestFullScreen(),document.documentElement.msRequestFullscreen()},getClient(){console.log(this.baseUrl,"99999999999999"),f.A.get(this.baseUrl+"/car/getProjectId").then((e=>{console.log(e,"查看请求的信息!!!!666"),this.ClientData=e.data,this.equimentId1=this.ClientData.cameras[0].sn,this.equimentId2=this.ClientData.cameras[1].sn,this.ClientData.cameras.length>2&&(this.equimentId3=this.ClientData.cameras[2].sn),this.getvideo(),this.getvideo2()})).catch((e=>{console.error("请求失败:",e)}))},updateButton(){this.submitLoading=!1,this.updateState=!this.updateState,this.updateState?(this.updateText="停止采集",this.buttonStatus=!0,this.flvPlayer.attachMediaElement(this.videoEl),this.flvPlayer.unload(),this.flvPlayer.on(b().Events.ERROR,((e,t)=>{e==b().ErrorTypes.MEDIA_ERROR?(console.log("媒体错误"),t==b().ErrorDetails.MEDIA_FORMAT_UNSUPPORTED&&console.log("媒体格式不支持")):e==b().ErrorTypes.NETWORK_ERROR?(console.log("网络错误"),t==b().ErrorDetails.NETWORK_STATUS_CODE_INVALID&&console.log("http状态码异常")):e==b().ErrorTypes.OTHER_ERROR&&console.log("其他异常：",t)})),this.flvPlayer.load(),this.flvPlayer.play(),this.connectMqttBroker(),this.connectMqttBroker2(),this.ClientData.cameras.length>2&&this.connectMqttBroker3(),this.buttonStatus2=!0,this.flvPlayer2.attachMediaElement(this.videoE2),this.flvPlayer2.unload(),this.flvPlayer2.on(b().Events.ERROR,((e,t)=>{e==b().ErrorTypes.MEDIA_ERROR?(console.log("媒体错误"),t==b().ErrorDetails.MEDIA_FORMAT_UNSUPPORTED&&console.log("媒体格式不支持")):e==b().ErrorTypes.NETWORK_ERROR?(console.log("网络错误"),t==b().ErrorDetails.NETWORK_STATUS_CODE_INVALID&&console.log("http状态码异常")):e==b().ErrorTypes.OTHER_ERROR&&console.log("其他异常：",t)})),this.flvPlayer2.load(),this.flvPlayer2.play()):(this.updateText="开始采集",this.disconnectMqttClient(),this.disconnectMqttClient2(),this.ClientData.cameras.length>2&&this.disconnectMqttClient3())},connectMqttBroker(){this.client&&this.client.end(),this.client=y.A.connect({protocol:"ws",hostname:this.mqttIp,path:"/mqtt",port:8083,username:"admin",password:"admin123",keepAliveInterval:60,mqttVersion:5,clientId:"myclientid_"+parseInt(1e4*Math.random(),10)}),this.client.on("connect",(()=>{console.log("Connected to MQTT broker","我连接了mqtt1"),this.client.subscribe("device/"+this.equimentId1+"/message/down/ivs_trigger/reply",{qos:0},((e,t)=>{console.log("Subscribed to topic999999",t),e||console.log("Subscribed to topic",t)})),this.client.subscribe("device/"+this.equimentId1+"/message/up/keep_alive",{qos:0},((e,t)=>{e||console.log("Subscribed to topic",t)})),this.client.subscribe("device/"+this.equimentId1+"/message/down/gpio_out/reply",{qos:0},((e,t)=>{e||console.log("Subscribed to topic",t)})),this.client.subscribe("device/"+this.equimentId1+"/message/up/ivs_result",{qos:0},((e,t)=>{e||console.log("Subscribed to my/topic")}))})),this.client.on("message",((e,t)=>{if(e=="device/"+this.equimentId1+"/message/up/ivs_result"){const e=JSON.parse(t.toString());if(console.log(e,"我是获取的数据1"),this.leftUrl="data:image/png;base64,"+e.payload.AlarmInfoPlate.result.PlateResult.full_image_content,this.triggerType=e.payload.AlarmInfoPlate.result.PlateResult.triggerType,this.carStatus)if(console.log("入场数据==================="),console.log(e,"查看获取到的入场数据"),this.payload=JSON.parse(t.toString()),1==e.payload.AlarmInfoPlate.result.PlateResult.is_fake_plate)this.showCar=this.base64Decode(e.payload.AlarmInfoPlate.result.PlateResult.license),this.dialogCarVisible=!0;else{let t=e.payload.AlarmInfoPlate.result.PlateResult.license;if("X+aXoF8="==t)return this.carNum="",this.licensePlateUnit=[],this.$message({message:"未识别到车牌",type:"warning"});var i=this.base64Decode(t);this.testCarNum(i,"自动获取"),console.log(this.carNum,"我是车牌号")}console.log(e,"我是获取的数据1")}if(e=="device/"+this.equimentId1+"/message/down/ivs_trigger/reply"){const e=JSON.parse(t.toString());console.log(e,"我是请求的数据")}if(e=="device/"+this.equimentId1+"/message/down/gpio_out/reply"){JSON.parse(t.toString())}if(e=="device/"+this.equimentId1+"/message/up/keep_alive"){const e=JSON.parse(t.toString());this.xintiaobao=1e3*e.timestamp}console.log(`Received message on topic ${e}: ${t.toString()}`)})),this.client.on("error",(e=>{console.error("MQTT error:",e)}))},connectMqttBroker2(){this.client2&&this.client2.end(),this.client2=y.A.connect({protocol:"ws",hostname:this.mqttIp,path:"/mqtt",port:8083,username:"admin",password:"admin123",keepAliveInterval:60,mqttVersion:5,clientId:"myclientid_2"+parseInt(1e4*Math.random(),10)}),this.client2.on("connect",(()=>{console.log("Connected to MQTT broker","我连接了mqtt2"),this.client2.subscribe("device/"+this.equimentId2+"/message/up/keep_alive",{qos:0},((e,t)=>{console.log("订阅心跳成功2",t),e||console.log("Subscribed to topic",t)})),this.client2.subscribe("device/"+this.equimentId2+"/message/up/ivs_result",{qos:0},((e,t)=>{console.log(t,"我是订阅成功2"),e||console.log("Subscribed to my/topic2")}))})),this.client2.on("message",((e,t)=>{if(e=="device/"+this.equimentId2+"/message/up/ivs_result"){const e=JSON.parse(t.toString());if(this.rightUrl="data:image/png;base64,"+e.payload.AlarmInfoPlate.result.PlateResult.full_image_content,!this.carStatus)if(console.log("出场数据================================="),console.log(e,"查看获取到的出场数据"),this.payload=JSON.parse(t.toString()),1==e.payload.AlarmInfoPlate.result.PlateResult.is_fake_plate)this.showCar=this.base64Decode(e.payload.AlarmInfoPlate.result.PlateResult.license),this.dialogCarVisible=!0;else{let t=e.payload.AlarmInfoPlate.result.PlateResult.license;if("X+aXoF8="==t)return this.carNum="",this.licensePlateUnit=[],this.$message({message:"未识别到车牌",type:"warning"});var i=this.base64Decode(t);this.testCarNum(i,"自动获取"),console.log(this.carNum,"我是车牌号")}console.log(e,"我是获取的数据2"),console.log(e.payload.AlarmInfoPlate.result.PlateResult.full_image_content,"查看图片路径"),this.triggerType=e.payload.AlarmInfoPlate.result.PlateResult.triggerType}if(e=="device/"+this.equimentId2+"/message/down/ivs_trigger/reply"){const e=JSON.parse(t.toString());console.log(e,"我是请求的数据")}if(e=="device/"+this.equimentId2+"/message/up/keep_alive"){const e=JSON.parse(t.toString());console.log(e,"我是心跳2"),this.xintiaobao2=1e3*e.timestamp}console.log(`Received message on topic ${e}: ${t.toString()}`)})),this.client2.on("error",(e=>{console.error("MQTT error:",e)}))},daozha_testCarNum(e){var t=this.daozhaValue.includes(e);console.log(t,"我是value请求的值"),t?(console.log("车牌号 "+e+"已抬杆，请稍后再试！"),this.$message({type:"warning",message:"车牌号 "+e+"已抬杆，请稍后再试！"})):(this.daozhaValue.push(e),this.handle_Taigan(e),setTimeout((()=>{var t=e;this.daozhaValue=this.daozhaValue.filter((e=>e!==t))}),1e5))},connectMqttBroker3(){this.client3&&this.client3.end(),this.client3=y.A.connect({protocol:"ws",hostname:this.mqttIp,path:"/mqtt",port:8083,username:"admin",password:"admin123",keepAliveInterval:60,mqttVersion:5,clientId:"myclientid_3"+parseInt(1e4*Math.random(),10)}),this.client3.on("connect",(()=>{console.log("Connected to MQTT broker","我连接了mqtt2"),this.client3.subscribe("device/"+this.equimentId3+"/message/up/keep_alive",{qos:0},((e,t)=>{console.log("订阅心跳成功2",t),e||console.log("Subscribed to topic",t)})),this.client3.subscribe("device/"+this.equimentId3+"/message/up/ivs_result",{qos:0},((e,t)=>{console.log(t,"我是订阅成功2"),e||console.log("Subscribed to my/topic2")})),this.client3.subscribe("device/"+this.equimentId3+"/message/down/gpio_out/reply",{qos:0},((e,t)=>{console.log("抬杆订阅成功",t),e||console.log("Subscribed to topic",t)}))})),this.client3.on("message",((e,t)=>{if(e=="device/"+this.equimentId3+"/message/up/ivs_result"){const e=JSON.parse(t.toString());console.log(e,"我是获取的第三个摄像头数据3");let o=e.payload.AlarmInfoPlate.result.PlateResult.license;if(console.log(o,"查看一下我是第三个摄像头的数据的getData"),"X+aXoF8="==o)return this.$message({message:"闸机未识别到车牌，请点击左上角第二个按钮触发重新识别",type:"warning"});var i=this.base64Decode(o);console.log(JSON.stringify({carNumber:i}),"我是提交的");var s=this.encrypt(JSON.stringify({carNumber:i}));console.log(this.requestToken,"发送请求的token"),f.A.get(this.baseUrl+"/car/getCarNumById",s,{headers:{"Content-Type":"application/json; charset=UTF-8",token:this.requestToken}}).then((e=>{console.log(e,"我是校验的车牌号是否存在");let t=e.data,s=t.some((e=>e===i));console.log(s,"确认"),s?this.daozha_testCarNum(i):(this.$message({type:"error",message:"车牌号"+i+"未提交运砂申请，抬杆失败！"}),this.carNum="",this.licensePlateUnit=[])})).catch((e=>{console.log(e,"我是错误")})),console.log(i,"我是第三个摄像头识别到的车牌号"),console.log(e,"我是获取的数据3")}if(e=="device/"+this.equimentId3+"/message/down/ivs_trigger/reply"){const e=JSON.parse(t.toString());console.log(e,"我是请求的数据")}if(e=="device/"+this.equimentId3+"/message/up/keep_alive"){const e=JSON.parse(t.toString());console.log(e,"我是心跳2"),this.xintiaobao2=1e3*e.timestamp}if(e=="device/"+this.equimentId3+"/message/down/gpio_out/reply"){const e=JSON.parse(t.toString());if(console.log(e,"我是抬杆回复的数据"),e.id&&(this.tgId=e.id.slice(0,12),this.tgid="myclientid_3",console.log(this.tgId,"我是抬杆的id"),console.log(this.tgid,"我是mqtt的id")),200==e.code&&this.tgid==this.tgId){this.$message({type:"success",message:"抬杆成功！请尽快通过！"});var o={clientId:this.ClientData.client.id,type:"in",carNumber:this.taigancarNumber,takeOff:!0};f.A.post(this.baseUrl+"/car/submitLiftRodData",o).then((e=>{console.log(e,"我是数据")})).catch((e=>{console.log(e,"我是错误")}))}else console.log(e,"我是抬杆错误的数据"),this.tgid==this.tgId&&this.$message({type:"error",message:"抬杆失败！"})}console.log(`Received message on topic ${e}: ${t.toString()}`)})),this.client3.on("error",(e=>{console.error("MQTT error:",e)}))},confirm(){if(console.log(this.carIs,"电器"),this.licensePlateUnit.length>=7)if(console.log("车牌是："+this.licensePlateUnit.join("")),this.carNum=this.licensePlateUnit.join(""),console.log(),this.carStatus)if(this.weight>5)this.testCarNum(this.carNum,"确认");else{var e=this.encrypt(JSON.stringify({carNumber:this.carNum}));f.A.get(this.baseUrl+"/car/getCarNumById",e,{headers:{"Content-Type":"application/json; charset=UTF-8",token:this.requestToken}}).then((e=>{console.log(e,"我是校验的车牌号是否存在");let t=e.data,i=t.some((e=>e===this.carNum));console.log(i,"确认"),i?(this.carStatus&&this.daozha_testCarNum(this.carNum),this.licensePlateDoor=!1):this.$message({type:"error",message:"车牌号未提交运砂申请，抬杆失败！"})})).catch((e=>{console.log(e,"我是错误")}))}else this.testCarNum(this.carNum,"确认")},SX(){window.location.reload()},TG(){this.client3&&this.disconnectMqttClient3(),this.connectMqttBroker3(),console.log("触发了我一次抬杆");var e={id:"camera3",sn:this.equimentId3,name:"ivs_trigger",version:"1.0",timestamp:**********,payload:{type:"ivs_trigger",body:{}}};console.log("重新获取"),this.client3.publish("device/"+this.equimentId3+"/message/down/ivs_trigger",JSON.stringify(e))},handle_Taigan(e){this.taigancarNumber=e,this.connectMqttBroker3();const t="myclientid_3"+parseInt(1e4*Math.random(),10);this.cremarsId.push(t),console.log("触发抬杆");var i={id:t,sn:this.equimentId3,name:"gpio_out",version:"1.0",timestamp:**********,payload:{type:"gpio_out",body:{delay:500,io:0,value:2}}};this.client3.publish("device/"+this.equimentId3+"/message/down/gpio_out",JSON.stringify(i))},searchCardNum(){var e={carNumber:this.formInline.cardNumber,startTime:this.formInline.startTime,endTime:this.formInline.endTime,pageNumber:1,pageSize:10};f.A.get(this.baseUrl+"/car/getDataByCarNumber",{params:e}).then((t=>{console.log(t.data,"历史数据"),console.log(e,"我是查询参数"),this.gridData=t.data.data,this.pageData.total=t.data.total,this.pageData.pageNumber=1})).catch((e=>{console.error(e)}))},getHistory(e){this.loading=!0,1==e&&(this.pageData.pageNumber=1);var t={carNumber:this.formInline.cardNumber,startTime:this.formInline.startTime,endTime:this.formInline.endTime,pageNumber:this.pageData.pageNumber,pageSize:this.pageData.pageSize};console.log(t,"我是查询参数"),f.A.get(this.baseUrl+"/car/getDataByCarNumber",{params:t}).then((e=>{console.log(e.data,"历史数据"),this.gridData=e.data.data,this.pageData.total=e.data.total,this.loading=!1})).catch((e=>{console.error(e)}))},handleSizeChange(e){this.pageData.pageSize=e,this.pageData.pageNumber=1,this.getHistory(),console.log(`每页 ${e} 条`)},handleCurrentChange(e){this.pageData.pageNumber=e,this.getHistory(),console.log(`当前页: ${e}`)},getProjectInfo(){var e={};f.A.post({NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_BASE_URL+"/openapi/station/list",e,{headers:{"Content-Type":"application/x-www-form-urlencoded",token:this.requestToken}}).then((e=>{console.log(e,"我是项目信息"),this.weightInfo=e.data.list})).catch((e=>{console.error("请求失败:",e)}))},testCarNum(e,t){console.log(JSON.stringify({carNumber:e}),"我是提交的");var i=this.encrypt(JSON.stringify({carNumber:e}));console.log(this.requestToken,"发送请求的token"),f.A.get(this.baseUrl+"/car/getCarNumById",i,{headers:{"Content-Type":"application/json; charset=UTF-8",token:this.requestToken}}).then((i=>{console.log(i,"我是校验的车牌号是否存在");let s=i.data,o=s.some((t=>t===e));if(console.log(o,"确认"),this.carIs=o,console.log(this.carIs,"我是校验的车牌号是否存在"),o&&"确认"==t)console.log("校验成功"),this.licensePlateDoor=!1,this.submitLoading=!1;else if(o&&"提交"==t){var l=this.leftUrl.match(/data:image\/(png|jpg|jpeg);base64,(.*)/),a=this.rightUrl.match(/data:image\/(png|jpg|jpeg);base64,(.*)/);if(l)var n=l[2];if(a)var r=a[2];this.ClientData.client.stationId,this.carNum,this.weight,this.carStatus;var c={weight:this.weight,carNumber:this.carNum,headImage:n,rearImage:r,dataType:this.carStatus?"in":"out"};if(this.weight<1)return this.$message({message:"提交重量不能小于1吨",type:"warning"}),void(this.submitLoading=!1);if(0==this.commitCishu&&this.weight>30&&1==this.carStatus)return this.dialogVisibleOne=!0,this.commitCishu++,void(this.submitLoading=!1);if(1==this.commitCishu&&this.weight>30&&1==this.carStatus)return this.dialogVisibleTwe=!0,this.commitCishu++,void(this.submitLoading=!1);if(0==this.commitCishu&&this.weight<=30&&0==this.carStatus)return this.dialogVisibleThree=!0,this.commitCishu++,void(this.submitLoading=!1);if(1==this.commitCishu&&this.weight<=30&&0==this.carStatus)return this.dialogVisibleFore=!0,this.commitCishu++,void(this.submitLoading=!1);f.A.post(this.baseUrl+"/car/submitData",c,{headers:{"Content-Type":"application/json; charset=UTF-8"}}).then((e=>{200==e.data.code?(this.submitLoading=!1,this.commitCishu=0,this.$message({message:"提交成功",type:"success"}),console.log("数据提交成功"),console.log(e.data,"提交"),this.leftUrl="",this.rightUrl="",this.weight="",this.licensePlateUnit=[],this.client&&(this.updateState=!1,this.updateButton(),this.client.subscribe("device/"+this.equimentId1+"/message/up/ivs_result",{qos:0},((e,t)=>{e?console.log("重新订阅获取信息"):console.log("已成功订阅获取信息")})),this.client2.subscribe("device/"+this.equimentId2+"/message/up/ivs_result",{qos:0},((e,t)=>{e?console.log("重新订阅获取信息"):console.log("已成功订阅获取信息")})),this.client3.subscribe("device/"+this.equimentId3+"/message/up/ivs_result",{qos:0},((e,t)=>{e?console.log("重新订阅获取信息"):console.log("已成功订阅获取信息")})))):(this.submitLoading=!1,this.commitCishu=0,this.$message({message:e.data.message,type:"warning"}))})).catch((e=>{console.error("请求失败:",e)}))}else if(this.carIs&&"自动获取"==t)this.carNum=e;else{if("自动获取"==t)return this.dialogCarIsVisible=!0,this.carNum="",void(this.licensePlateUnit=[]);this.$message({message:"车主未提交运砂申请,禁止通过!",type:"warning"})}console.log(this.carIs,"66666")})).catch((e=>{console.error("请求失败:",e)}))},getmuhuSearch(e){this.carValue=e,console.log(e,"我是value");var t={numberStr:e};console.log(t,"我是参数"),f.A.get(this.baseUrl+"/car/getCarNumById",{params:t}).then((e=>{this.searchResults=[],e.data.length>0&&(this.searchResults=e.data,console.log(e.data))})).catch((e=>{console.error(e)}))},submitData(){this.submitLoading=!0,console.log("提交"),console.log(this.carNum,"我是提交的车牌号"),""!=this.carNum&&void 0!=this.carNum?(this.testCarNum(this.carNum,"提交"),console.log(this.carIs,"我是carIs66")):this.$message({message:"请输入车牌号",type:"warning"})},encrypt(e){const t=S().AES.encrypt(e,w,{mode:S().mode.ECB,padding:S().pad.Pkcs7});return t.toString()},CarisFalse(){this.dialogCarIsVisible=!1},getToken_(){var e=JSON.stringify({appId:"44",secret:"22"});f.A.post({NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_BASE_URL+"/openapi/token/getToken",e,{headers:{"Content-Type":"application/json; charset=UTF-8"}}).then((e=>{console.log(e,"我是token设备的"),this.requestToken=e.data.token,console.log(this.requestToken,"获取token")})).catch((e=>{console.error("请求失败:",e)}))},handleClose(){this.dialogCarVisible=!1},handleCloseOne(){this.dialogVisibleOne=!1},handleCloseTwe(){this.dialogVisibleTwe=!1},handleCloseThree(){this.dialogVisibleThree=!1},handleCloseFore(){this.dialogVisibleFore=!1},carTrue(e){if("获取"==e){this.dialogCarVisible=!1;let e=this.payload.payload.AlarmInfoPlate.result.PlateResult.license;if("X+aXoF8="==e)return this.$message({message:"未识别到车牌",type:"warning"});var t=this.base64Decode(e);this.testCarNum(t,"自动获取")}else this.dialogCarVisible=!1,this.carNum="",this.licensePlateUnit=[]},getDun(){this.isRuning||(this.timer&&(clearInterval(this.timer),this.timer=null),this.timer=setInterval((()=>{f.A.get(this.baseUrl+"/weight/getWeight",{headers:{"Content-Type":"application/json; charset=UTF-8"}}).then((e=>{if(200==e.data.code)this.weightErrorInfoNum=0,this.colorStyle.fontSize="30px",this.colorStyle.color="#409EFF",this.weight=e.data.data;else if(202==e.data.code&&(this.weightErrorInfoNum++,this.weightErrorInfoNum>=10)){var t=e.data.message.substring(0,7);this.endData=e.data.message.substring(7),this.colorStyle.fontSize="20px",this.colorStyle.color="red",this.weight=t}})).catch((e=>{}))}),1e3))},base64Decode(e){let t=atob(e),i=t.length,s=new Uint8Array(i);for(let a=0;a<i;a++)s[a]=t.charCodeAt(a);let o=new TextDecoder,l=o.decode(s);return console.log(l,"查看一下车牌号"),this.licensePlateUnit=l.split(""),l},disconnectMqttClient(){this.client.end((()=>{console.log("Disconnected from MQTT broker")}))},disconnectMqttClient2(){this.client2.end((()=>{console.log("Disconnected from MQTT broker")}))},disconnectMqttClient3(){this.client3.end((()=>{console.log("Disconnected from MQTT broker")}))},replayget(){this.submitLoading=!1;var e={id:"camera1",sn:this.equimentId1,name:"ivs_trigger",version:"1.0",timestamp:**********,payload:{type:"ivs_trigger",body:{}}},t={id:"camera",sn:this.equimentId2,name:"ivs_trigger",version:"1.0",timestamp:**********,payload:{type:"ivs_trigger",body:{}}};console.log("重新获取"),this.client.publish("device/"+this.equimentId1+"/message/down/ivs_trigger",JSON.stringify(e)),console.log(this.equimentId2,e,"我是第二个摄像头id"),this.client2.publish("device/"+this.equimentId2+"/message/down/ivs_trigger",JSON.stringify(t))},getvideo(){var e={type:"get_live_stream_type",module:"BUS_WEB_REQUEST"};console.log("http://"+this.ClientData.cameras[0].ip+"/request.php","查看地址"),f.A.post("http://"+this.ClientData.cameras[0].ip+"/request.php",e).then((e=>{this.token=e.data.body.token,b().isSupported()?(this.videoEl=document.getElementById("videoEl"),this.flvPlayer=b().createPlayer({type:"flv",url:`ws://${this.ClientData.cameras[0].ip}:9080/ws.flv?token=${this.token}&channel=0`}),this.flvPlayer.attachMediaElement(this.videoEl),this.flvPlayer.on(b().Events.ERROR,((e,t)=>{e==b().ErrorTypes.MEDIA_ERROR?(console.log("媒体错误"),t==b().ErrorDetails.MEDIA_FORMAT_UNSUPPORTED&&console.log("媒体格式不支持")):e==b().ErrorTypes.NETWORK_ERROR?(console.log("网络错误"),t==b().ErrorDetails.NETWORK_STATUS_CODE_INVALID&&console.log("http状态码异常")):e==b().ErrorTypes.OTHER_ERROR&&console.log("其他异常：",t)})),this.flvPlayer.load()):console.log("FLV.js is not supported in this browser.")})).catch((e=>{console.error("请求失败:",e)}))},getvideo2(){var e={type:"get_live_stream_type",module:"BUS_WEB_REQUEST"};f.A.post("http://"+this.ClientData.cameras[1].ip+"/request.php",e).then((e=>{this.token2=e.data.body.token,b().isSupported()?(this.videoE2=document.getElementById("videoE2"),this.flvPlayer2=b().createPlayer({type:"flv",url:`ws://${this.ClientData.cameras[1].ip}:9080/ws.flv?token=${this.token2}&channel=0`}),this.flvPlayer2.attachMediaElement(this.videoE2),this.flvPlayer2.on(b().Events.ERROR,((e,t)=>{e==b().ErrorTypes.MEDIA_ERROR?(console.log("媒体错误"),t==b().ErrorDetails.MEDIA_FORMAT_UNSUPPORTED&&console.log("媒体格式不支持")):e==b().ErrorTypes.NETWORK_ERROR?(console.log("网络错误"),t==b().ErrorDetails.NETWORK_STATUS_CODE_INVALID&&console.log("http状态码异常")):e==b().ErrorTypes.OTHER_ERROR&&console.log("其他异常：",t)})),this.flvPlayer2.load()):console.log("FLV.js is not supported in this browser.")})).catch((e=>{console.error("请求失败:",e)}))},flv_start(){console.log("111"),this.shebei1Status&&this.getvideo(),this.buttonStatus=!0,this.flvPlayer.attachMediaElement(this.videoEl),this.flvPlayer.unload(),this.flvPlayer.on(b().Events.ERROR,((e,t)=>{e==b().ErrorTypes.MEDIA_ERROR?(console.log("媒体错误"),t==b().ErrorDetails.MEDIA_FORMAT_UNSUPPORTED&&console.log("媒体格式不支持")):e==b().ErrorTypes.NETWORK_ERROR?(console.log("网络错误"),t==b().ErrorDetails.NETWORK_STATUS_CODE_INVALID&&console.log("http状态码异常")):e==b().ErrorTypes.OTHER_ERROR&&console.log("其他异常：",t)})),this.flvPlayer.load(),this.flvPlayer.play()},flv_start2(){this.buttonStatus2=!0,this.flvPlayer2.attachMediaElement(this.videoE2),this.flvPlayer2.unload(),this.flvPlayer2.on(b().Events.ERROR,((e,t)=>{e==b().ErrorTypes.MEDIA_ERROR?(console.log("媒体错误"),t==b().ErrorDetails.MEDIA_FORMAT_UNSUPPORTED&&console.log("媒体格式不支持")):e==b().ErrorTypes.NETWORK_ERROR?(console.log("网络错误"),t==b().ErrorDetails.NETWORK_STATUS_CODE_INVALID&&console.log("http状态码异常")):e==b().ErrorTypes.OTHER_ERROR&&console.log("其他异常：",t)})),this.flvPlayer2.load(),this.flvPlayer2.play()},flv_end(){this.buttonStatus=!1,this.flvPlayer&&(this.flvPlayer.pause(),this.flvPlayer.unload())},flv_end2(){this.buttonStatus2=!1,this.flvPlayer2&&(this.flvPlayer2.pause(),this.flvPlayer2.unload())},flv_full(){var e=document.getElementById("videoEl");e.requestFullscreen?e.requestFullscreen():e.webkitRequestFullScreen&&e.webkitRequestFullScreen()},flv_full2(){var e=document.getElementById("videoE2");e.requestFullscreen?e.requestFullscreen():e.webkitRequestFullScreen&&e.webkitRequestFullScreen()},showHistory(){this.dialogTableVisible=!0,this.tableState=!0,this.pageData.pageNumber=1,this.formInline.cardNumber="",this.getHistory(),this.formInline.cardNumber=""},handleClick(e){console.log(e,"7777777"),this.licensePlateUnit=e.split(""),this.licensePlateUnitLength=this.licensePlateUnit.length},handleSelect(e){this.licensePlateUnit[0]=e.target.value,this.licensePlateUnitLength=this.licensePlateUnit.length,this.licensePlateDoor=!0,console.log(this.licensePlateUnit.length,"我是监听的值,licensePlateUnit的长度")},license1(e){console.log(e,"我是监听的值1"),e.target.value&&console.log("我进入了这个监听1"),this.licensePlateUnit[1]=e.target.value},license2(e){console.log(e,"我是监听的值2"),this.licensePlateUnit[2]=e.target.value.split("").map((e=>/[a-zA-Z]/.test(e)?e.toUpperCase():(/[0-9]/.test(e),e))).join("")},license3(e){console.log(e,"我是监听的值"),this.licensePlateUnit[3]=e.target.value},license4(e){console.log(e,"我是监听的值"),this.licensePlateUnit[4]=e.target.value},license5(e){console.log(e,"我是监听的值"),this.licensePlateUnit[5]=e.target.value},license6(e){console.log(e,"我是监听的值"),this.licensePlateUnit[6]=e.target.value},license7(e){console.log(e,"我是监听的值"),this.licensePlateUnit[7]=e.target.value},pickOn(e){console.log(this.licensePlateUnit.join(""),"我是选中的值66"),this.licensePlateDoor=!0,this.licensePlateUnit.length<=7&&(this.licensePlateUnit.push(e),this.licensePlateUnitLength=this.licensePlateUnit.length,this.getmuhuSearch(this.licensePlateUnit.join("")))},delCarNo(){console.log(this.licensePlateUnit,"888888888888"),this.licensePlateUnit.pop(),this.getmuhuSearch(this.licensePlateUnit.join("")),0===this.licensePlateUnit.length&&(this.submitLoading=!1,this.licensePlateDoor=!1,this.licensePlateUnitLength=0,this.carNum="")},close(){this.getmuhuSearch(""),this.submitLoading=!1,this.licensePlateDoor=!1,this.licensePlateUnit=[],this.licensePlateUnitLength=0,this.carNum=""}}},P=T,C=(0,r.A)(P,u,g,!1,null,null,null),I=C.exports,E=function(){var e=this;e._self._c;return e._m(0)},k=[function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("div",{attrs:{id:"loader-wrapper"}},[t("div",{attrs:{id:"loader"}}),t("div",{staticClass:"loader-section section-left"}),t("div",{staticClass:"class=",attrs:{"loader-section":"","section-right":""}}),t("div",{staticClass:"load_title"},[e._v(" 正在启动系统中，请耐心等待 ")])])])}],N={data(){return{}},mounted(){},created(){this.getClint()},methods:{getClint(){console.log(66666666),f.A.get("http://127.0.0.1:8077/car/getProjectId").then((e=>{console.log(e),0==e.data.code?this.$router.push("/main"):this.getClint()})).catch((e=>{this.getClint(),console.error("请求失败:",e)}))}}},D=N,R=(0,r.A)(D,E,k,!1,null,null,null);R.exports;s["default"].use(d.Ay);var x=new d.Ay({mode:"history",routes:[{path:"/",component:I},{path:"/main",component:I}]}),U=i(9143),O=i.n(U);s["default"].config.productionTip=!1,s["default"].use(O()),new s["default"]({router:x,render:e=>e(h),strict:!1}).$mount("#app")},2185:function(e,t,i){"use strict";e.exports=i.p+"img/beijingtu.0a582fd6.png"},8667:function(e,t,i){"use strict";e.exports=i.p+"img/yezi2.c2f43105.png"},477:function(){}},t={};function i(s){var o=t[s];if(void 0!==o)return o.exports;var l=t[s]={id:s,loaded:!1,exports:{}};return e[s].call(l.exports,l,l.exports,i),l.loaded=!0,l.exports}i.m=e,function(){i.amdO={}}(),function(){var e=[];i.O=function(t,s,o,l){if(!s){var a=1/0;for(h=0;h<e.length;h++){s=e[h][0],o=e[h][1],l=e[h][2];for(var n=!0,r=0;r<s.length;r++)(!1&l||a>=l)&&Object.keys(i.O).every((function(e){return i.O[e](s[r])}))?s.splice(r--,1):(n=!1,l<a&&(a=l));if(n){e.splice(h--,1);var c=o();void 0!==c&&(t=c)}}return t}l=l||0;for(var h=e.length;h>0&&e[h-1][2]>l;h--)e[h]=e[h-1];e[h]=[s,o,l]}}(),function(){i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,{a:t}),t}}(),function(){i.d=function(e,t){for(var s in t)i.o(t,s)&&!i.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}}(),function(){i.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){i.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){i.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){i.p="/"}(),function(){var e={524:0};i.O.j=function(t){return 0===e[t]};var t=function(t,s){var o,l,a=s[0],n=s[1],r=s[2],c=0;if(a.some((function(t){return 0!==e[t]}))){for(o in n)i.o(n,o)&&(i.m[o]=n[o]);if(r)var h=r(i)}for(t&&t(s);c<a.length;c++)l=a[c],i.o(e,l)&&e[l]&&e[l][0](),e[l]=0;return i.O(h)},s=self["webpackChunkquarryweb"]=self["webpackChunkquarryweb"]||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))}();var s=i.O(void 0,[504],(function(){return i(1582)}));s=i.O(s)})();
//# sourceMappingURL=app.c811722b.js.map
package com.xiaodu.serialportdemo.demos.web;

import com.xiaodu.serialportdemo.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;


@RestController
@RequestMapping("updateVersion")
@Api(tags = "更新版本模块")
public class UpdateVersionController {

    @GetMapping("/update")
    @ApiOperation("更新版本")
    public Result updateVersion() throws Exception {
        String jarUrlPath = "http://127.0.0.1:8099/serialport/SerialPortDemo-0.0.1-SNAPSHOT-1.0.0.jar";

        // 执行 .bat 脚本的路径
        String batScriptPath = "E:\\jar\\updateJar.bat";

        URL url= new URL(jarUrlPath);

        try (InputStream in = url.openStream()) {
            FileOutputStream fileOutputStream = new FileOutputStream("E:\\jar\\SerialPortDemo-0.0.1-SNAPSHOT-new.jar");
            byte[] buffer = new byte[4096];
            int byteRead;
            while ((byteRead = in.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, byteRead);
            }
            // 创建一个进程执行 BAT 脚本
            Process process = Runtime.getRuntime().exec(batScriptPath);

            // 等待脚本执行完成
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("脚本执行成功！");
            } else {
                System.out.println("脚本执行失败，退出代码：" + exitCode);
            }
        }
        //等待保存完成
//        Thread.sleep(10000);

        return Result.ok();
    }


}

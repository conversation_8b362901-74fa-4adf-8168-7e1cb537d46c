package com.xiaodu.serialportdemo.mqtt;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.xiaodu.serialportdemo.config.GpioConfig;
import com.xiaodu.serialportdemo.constant.SerialPortConstant;
import com.xiaodu.serialportdemo.demos.web.CarController;
import com.xiaodu.serialportdemo.utils.RestTemplateUtils;
import com.xiaodu.serialportdemo.vo.LiftRodDataVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;

/**
 * MQTT消息回调处理器
 * 负责监听和处理MQTT消息，主要用于抬杆控制
 */
 @Data
 @Slf4j
 public class JwMqttCallback implements MqttCallbackExtended {

    // 常量定义
    private static final int RECONNECT_DELAY_SECONDS = 5;
    private static final int CACHE_SIZE = 100;
    private static final String TARGET_CAMERA_IP_SUFFIX = "2";
    private static final String RAISE_STATUS = "Raise";
    private static final String DROP_STATUS = "Drop";
    private static final int SUCCESS_CODE = 200;

    // 替换为Caffeine缓存（线程安全且性能更优）
    private static final Cache<String, String> messageCache =
            Caffeine.newBuilder()
                    .maximumSize(CACHE_SIZE)
                    .build();

    // 单线程调度器（守护线程）-用于延时落杆
    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor(
            r->{Thread t = new Thread(r,"mqtt-delay-executor");
            t.setDaemon(true);
            return t;
            });

    //任务存储(设备ID -> 任务)
    private final ConcurrentHashMap<String, ScheduledFuture<?>> taskMap = new ConcurrentHashMap<>();

    // 依赖注入
    @Resource
    private MQTTConfig mqttConfig;

    @Autowired
    private CarController carController;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private GpioConfig gpioConfig;

    // MQTT客户端
    private final JwMqttClient mqttClientOne;
    private final JwMqttClient mqttClientTwo;

    private volatile boolean subscribed = false;

    public JwMqttCallback(JwMqttClient mqttClientOne, JwMqttClient mqttClientTwo) {
        this.mqttClientOne = mqttClientOne;
        this.mqttClientTwo = mqttClientTwo;
    }

    /**
     * MQTT连接丢失时的处理
     * 会自动尝试重连，直到连接成功
     */
     @Override
     public void connectionLost(Throwable throwable) {
         log.error("MQTT连接断开，{}秒后尝试重连: {}", RECONNECT_DELAY_SECONDS, throwable.getMessage());

                 // mqttClientOne 重连
                 if (!mqttClientOne.getClient().isConnected()) {
                     log.warn("mqtt重连尝试");
                     try {
                         mqttClientOne.getClient().reconnect();
                     } catch (MqttException e) {
                         throw new RuntimeException(e);
                     }
                     if (mqttClientOne.getClient().isConnected()) {
                         log.info("mqtt重连成功");
                     }
                 }

                 // 如果连上了，关闭重连任务
                 if (mqttClientOne.getClient().isConnected()) {
                     log.info("MQTT客户端重连成功，取消重连任务");
                 }
     }


    /**
     * 接收到MQTT消息时的处理
     * 主要处理两种消息：抬杆指令和GPIO回执
     */
     @Override
     public void messageArrived(String topic, MqttMessage mqttMessage) throws Exception {
        String messagePayload = new String(mqttMessage.getPayload(), StandardCharsets.UTF_8);

        // 获取设备信息
        Map<String, String> deviceInfo = getDeviceInfo();
        if (deviceInfo == null) {
            log.error("获取设备信息失败，无法处理消息");
            return;
        }
        String clientId = deviceInfo.get("id");
        String cameraId = deviceInfo.get("cameraId");
        String cameraSN = deviceInfo.get("cameraSN");
        String currentClientId = deviceInfo.get("id");

        // 处理抬杆指令消息
        if (topic.equals(MqttConstants.TAKE_OFF+"_"+clientId)) {
            handleLiftRodCommand(messagePayload, currentClientId, cameraId, cameraSN);
        }
        // 处理GPIO回执消息
        else if (topic.equals(MqttConstants.REPLY_FRONT + cameraSN + MqttConstants.REPLY_LAST)) {
            handleGpioReply(messagePayload, cameraId, deviceInfo);
        }
     }

    /**
     * 处理抬杆指令消息
     */
     private void handleLiftRodCommand(String messagePayload, String currentClientId, String cameraId, String cameraSN) {
        try {
            JSONObject messageJson = new JSONObject(messagePayload);
            String clientId = messageJson.getStr("clientId");

            // 验证客户端ID是否匹配
            if (!currentClientId.equals(clientId)) {
                return;
            }

            // 提取消息数据
            String carNumber = messageJson.getStr("carNum");
            String billNo = messageJson.getStr("billNo");
            String status = messageJson.getStr("status");
            Integer delayTime = messageJson.getInt("time");

            // 缓存消息数据
            cacheMessageData(clientId, carNumber, billNo, status);

            // 根据状态执行相应操作
            if (StringUtils.isNotBlank(status)) {
                if (RAISE_STATUS.equals(status)) {
                    log.warn("设备开始抬杆");
                    System.out.println("设备开始抬杆");
                    // 执行抬杆操作
                    delayTime = 30 * 1000;
                    executeRaiseOperation(cameraId, cameraSN, delayTime);
                } else if (DROP_STATUS.equals(status)) {
                    log.warn("设备开始落杆");
                    System.out.println("设备开始落杆");
                    // 执行落杆操作
                    executeDropOperation(cameraId, cameraSN);
                }
            } else if (StringUtils.isBlank(status)) {
                // 状态为空时直接返回
                return;
            }

        } catch (Exception e) {
            log.error("处理抬杆指令失败", e);
        }
     }

    /**
     * 缓存消息数据
     */
     private void cacheMessageData(String clientId, String carNumber, String billNo, String status) {
        messageCache.put("clientId", clientId);

        // 只有在单据号和车牌号不为空时才缓存
        if (StringUtils.isNotBlank(billNo) ) {
            messageCache.put("billNo", billNo);
        }
        if(StringUtils.isNotBlank(carNumber)){
            messageCache.put("carNumber", carNumber);
        }

        messageCache.put("status", status);
     }

    /**
     * 执行抬杆操作
     */
     private void executeRaiseOperation(String cameraId, String cameraSN, Integer delayTime) {
        try {
            // 发送抬杆指令
            JSONObject raiseMessage = buildGpioRaiseMessage(cameraId, cameraSN);
            String gpioTopic = "device/" + cameraSN + "/message/down/gpio_out";
            mqttClientTwo.publish(raiseMessage.toString(), gpioTopic);

            log.info("设备{}抬杆指令发送成功", cameraId);

            //  新增：判断延时时间逻辑
            if (delayTime == null || delayTime <= 0) {
                // 默认30秒后自动落杆
                scheduleDropOperation(cameraId, cameraSN, 30);
                log.info("设备{}使用默认30秒自动落杆", cameraId);
            } else {
                // 使用传入的延时时间（转换为秒）
                int delaySeconds = delayTime * 60; // 转换分钟为秒
                scheduleDropOperation(cameraId, cameraSN, delaySeconds);
                log.info("设备{}设置{}分钟({}秒)后自动落杆", cameraId, delayTime, delaySeconds);
            }


        } catch (Exception e) {
            log.error("执行抬杆操作失败", e);
        }
     }

    /**
     * 执行落杆操作
     */
     private void executeDropOperation(String cameraId, String cameraSN) {
        try {
            JSONObject dropMessage = buildGpioDropMessage(cameraId, cameraSN);
            String gpioTopic = "device/" + cameraSN + "/message/down/gpio_out";
            mqttClientTwo.publish(dropMessage.toString(), gpioTopic);
        } catch (Exception e) {
            log.error("执行落杆操作失败", e);
        }
     }

    /**
     * 提交定时落杆任务(自动取消同意设备的旧任务)
     * @param cameraId 摄像头ID
     * @param cameraSN 摄像头SN
     * @param delaySeconds 延时秒数
     */
     public void scheduleDropOperation(String cameraId, String cameraSN, Integer delaySeconds){
         //取消已存在的任务
         cancelTask(cameraId);

         //提交新任务
         ScheduledFuture<?> future = executor.schedule(()->{
             try {
                 log.info("设备{}开始执行自动落杆，延时{}秒", cameraId, delaySeconds);

                 JSONObject dropMessage = buildGpioDropMessage(cameraId, cameraSN);
                 String gpioTopic = "device/" + cameraSN + "/message/down/gpio_out";
                 mqttClientTwo.publish(dropMessage.toString(), gpioTopic);
                 log.info("设备{}自动落杆指令发送成功", cameraId);

             }catch (Exception e){
                 log.error("设备{}发送消息失败",cameraId, e);

             }finally {
                 taskMap.remove(cameraId);
                 log.debug("设备{}落杆任务已清理", cameraId);

             }
         },delaySeconds,TimeUnit.SECONDS);
         taskMap.put(cameraId,future);
     }

    /**
     * 取消定时落杆任务
     * @param cameraId 摄像头ID()
     */
    public void cancelTask(String cameraId) {
        ScheduledFuture<?> future = taskMap.get(cameraId);
        if(future!=null && !future.isDone()){
            future.cancel(false);
        }
    }

    /**
     * 获取剩余延时时间
     * @param cameraId 摄像头ID
     * @param timeUnit 时间单位
     * @return 剩余延时时间
     */
    public Optional<Long> getRemainingDelay(String cameraId,TimeUnit timeUnit){
        return Optional.ofNullable(taskMap.get(cameraId)).map(future->future.getDelay(timeUnit));
    }

    /**
     * 优雅关闭
     */
    @PreDestroy
    // 标注该方法在Bean销毁前执行，通常用于资源清理
    public void shutdown() {
        // 关闭executor，不再接受新任务，但已提交的任务会继续执行
        executor.shutdown();
        try {
            // 等待executor在5秒内完成所有任务
            if(executor.awaitTermination(5, TimeUnit.SECONDS)){
                // 如果在5秒内所有任务都已完成，则立即关闭executor
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 如果等待过程中线程被中断，则立即关闭executor
            executor.shutdownNow();
            // 重新设置当前线程的中断状态
            Thread.currentThread().interrupt();
        }
}

    /**
     * 处理GPIO回执消息
     */
     private void handleGpioReply(String messagePayload, String cameraId, Map<String, String> deviceInfo) {
        try {
            JSONObject replyJson = new JSONObject(messagePayload);
            String replyId = replyJson.getStr("id");
            Integer responseCode = replyJson.getInt("code");

            // 验证设备ID是否匹配
            if (!replyId.equals(cameraId)) {
                return;
            }

            // 构建抬杆数据记录
            LiftRodDataVo liftRodData = buildLiftRodDataFromCache(responseCode);

            // 提交抬杆日志
            submitLiftRodLog(liftRodData, deviceInfo);

            // 清空缓存
            messageCache.invalidateAll();
            log.info("缓存已清空");

        } catch (Exception e) {
            log.error("处理GPIO回执失败", e);
        }
     }

    /**
     * 从缓存构建抬杆数据记录
     */
     private LiftRodDataVo buildLiftRodDataFromCache(Integer responseCode) {
        LiftRodDataVo liftRodData = new LiftRodDataVo();
        liftRodData.setType("out");
        liftRodData.setClientId(messageCache.getIfPresent("clientId"));
        if(responseCode==null || responseCode!=SUCCESS_CODE){
            liftRodData.setTakeOff(false);
        }else {
            liftRodData.setTakeOff(true);
        }

        String carNumber = messageCache.getIfPresent("carNumber");
        String billNo = messageCache.getIfPresent("billNo");
        String status = messageCache.getIfPresent("status");

        // 只有在车牌号和单据号不为空且状态为空时才设置
        if (StringUtils.isNotBlank(carNumber)) {
            liftRodData.setCarNumber(carNumber);
        }
        if(StringUtils.isNotBlank(billNo)){
            liftRodData.setBillNo(billNo);
        }

        liftRodData.setStatus(status);
        return liftRodData;
     }

    /**
     * 提交抬杆日志
     */
     private void submitLiftRodLog(LiftRodDataVo liftRodData, Map<String, String> deviceInfo) {
        try {
            Map<String, String> authParams = new HashMap<>();
            authParams.put("token", deviceInfo.get("token"));
            authParams.put("timestamp", deviceInfo.get("timestamp"));

            RestTemplateUtils.post(
                SerialPortConstant.SUBMIT_LIFTRODDATA_URL,
                authParams,
                liftRodData,
                JSONObject.class,
                ""
            );
        } catch (Exception e) {
            log.error("提交抬杆日志失败", e);
        }
     }

    /**
     * 构建抬杆GPIO消息
     */
     private JSONObject buildGpioRaiseMessage(String cameraId, String cameraSN) {
        return buildGpioMessage(cameraId, cameraSN, gpioConfig.getIoMapping().getRaise());
     }

    /**
     * 构建落杆GPIO消息
     */
     private JSONObject buildGpioDropMessage(String cameraId, String cameraSN) {
        return buildGpioMessage(cameraId, cameraSN, gpioConfig.getIoMapping().getDrop());
     }

    /**
     * 构建GPIO控制消息的通用方法
     * 支持道闸运行参数和安全保护参数，适配8_D6直流无刷道闸主板
     */
     private JSONObject buildGpioMessage(String cameraId, String cameraSN, int ioValue) {
        JSONObject message = new JSONObject();
        message.put("id", cameraId);
        message.put("sn", cameraSN);
        message.put("name", "gpio_out");
        message.put("version", "1.0");
        message.put("timestamp", System.currentTimeMillis() / 1000);

        JSONObject payload = new JSONObject();
        payload.put("type", "gpio_out");

        JSONObject body = new JSONObject();
        // 基础GPIO控制参数
        body.put("delay", gpioConfig.getDelay());
        body.put("io", ioValue);
        body.put("value", gpioConfig.getOutputValue());
        
        // 道闸运行参数
        JSONObject barrierParams = new JSONObject();
        barrierParams.put("force_level", gpioConfig.getBarrier().getForceLevel());
        barrierParams.put("speed_level", gpioConfig.getBarrier().getSpeedLevel());
        if (ioValue == gpioConfig.getIoMapping().getRaise()) {
            // 抬杆时间
            barrierParams.put("operation_time", gpioConfig.getBarrier().getRaiseTime());
            barrierParams.put("action", "raise");
        } else {
            // 落杆时间
            barrierParams.put("operation_time", gpioConfig.getBarrier().getDropTime());
            barrierParams.put("action", "drop");
        }
        barrierParams.put("hold_time", gpioConfig.getBarrier().getHoldTime());
        body.put("barrier", barrierParams);
        
        // 安全保护参数
        JSONObject safetyParams = new JSONObject();
        safetyParams.put("obstacle_detect", gpioConfig.getSafety().isObstacleDetect());
        safetyParams.put("auto_reverse", gpioConfig.getSafety().isAutoReverse());
        safetyParams.put("emergency_stop", gpioConfig.getSafety().isEmergencyStop());
        safetyParams.put("max_force", gpioConfig.getSafety().getMaxForce());
        safetyParams.put("pressure_limit", gpioConfig.getSafety().getPressureLimit());
        body.put("safety", safetyParams);
        
        // 信号控制参数
        JSONObject signalParams = new JSONObject();
        signalParams.put("pulse_width", gpioConfig.getSignal().getPulseWidth());
        signalParams.put("pulse_interval", gpioConfig.getSignal().getPulseInterval());
        signalParams.put("signal_level", gpioConfig.getSignal().getSignalLevel());
        signalParams.put("drive_current", gpioConfig.getSignal().getDriveCurrent());
        signalParams.put("brake_enable", gpioConfig.getSignal().isBrakeEnable());
        body.put("signal", signalParams);

        payload.put("body", body);
        message.put("payload", payload);
        
        log.info("GPIO消息构建完成: action={}, force={}, speed={}, safety={}", 
                 ioValue == gpioConfig.getIoMapping().getRaise() ? "抬杆" : "落杆",
                 gpioConfig.getBarrier().getForceLevel(),
                 gpioConfig.getBarrier().getSpeedLevel(),
                 gpioConfig.getSafety().isObstacleDetect() ? "启用障碍检测" : "关闭障碍检测");
        return message;
     }

    /**
     * 获取设备信息（包括认证信息和摄像头信息）
     */
     public Map<String, String> getDeviceInfo() {
        try {
            // 获取MAC地址
            String macAddress = getMacAddress();
            if (macAddress == null) {
                log.error("无法获取MAC地址");
                return null;
            }

            // 生成认证信息
            Map<String, String> authInfo = generateAuthInfo(macAddress);

            // 获取项目配置
            JSONObject projectConfig = getProjectConfig(authInfo);
            if (projectConfig == null) {
                log.error("无法获取项目配置");
                return null;
            }

            // 查找目标摄像头
            return findTargetCamera(projectConfig, authInfo, macAddress);

        } catch (Exception e) {
            log.error("获取设备信息失败", e);
            return null;
        }
     }

    /**
     * 获取MAC地址
     */
     private String getMacAddress() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            for (NetworkInterface netInterface : Collections.list(interfaces)) {
                // 过滤掉Wi-Fi、WLAN、回环网卡，仅获取有线网卡
                if (isValidNetworkInterface(netInterface)) {
                    byte[] macBytes = netInterface.getHardwareAddress();
                    if (macBytes != null) {
                        StringBuilder macBuilder = new StringBuilder();
                        for (int i = 0; i < macBytes.length; i++) {
                            macBuilder.append(String.format("%02X%s", macBytes[i],
                                (i < macBytes.length - 1) ? "-" : ""));
                        }
                        String macAddress = macBuilder.toString();
                        log.info("获取到MAC地址：{}", macAddress);
                        return macAddress;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取MAC地址失败", e);
        }
        return null;
     }

    /**
     * 判断是否为有效的网络接口
     */
     private boolean isValidNetworkInterface(NetworkInterface netInterface) throws Exception {
        return !netInterface.getDisplayName().contains("Wi-Fi")
                && !netInterface.getDisplayName().contains("WLAN")
                && !netInterface.isLoopback()
                && netInterface.getHardwareAddress() != null;
     }

    /**
     * 生成认证信息（Token和时间戳）
     */
     private Map<String, String> generateAuthInfo(String macAddress) {
        long timestamp = System.currentTimeMillis();

        // 计算MD5生成Token
        String timestampMD5 = DigestUtils.md5Hex(String.valueOf(timestamp));
        String clientIdMD5 = DigestUtils.md5Hex(macAddress);
        String secretKey = SerialPortConstant.CLIENT_KEY + timestampMD5;

        String token = JWT.create()
                .setPayload("auth_id", clientIdMD5)
                .setPayload("timestamp", timestamp)
                .setKey(secretKey.getBytes())
                .sign();

        Map<String, String> authInfo = new HashMap<>();
        authInfo.put("token", token);
        authInfo.put("timestamp", String.valueOf(timestamp));
        authInfo.put("clientId", macAddress);
        return authInfo;
     }

    /**
     * 获取项目配置信息
     */
     private JSONObject getProjectConfig(Map<String, String> authInfo) {
        try {
            Map<String, String> requestParams = new HashMap<>();
            requestParams.put("token", authInfo.get("token"));
            requestParams.put("timestamp", authInfo.get("timestamp"));

            ResponseEntity<JSONObject> response = RestTemplateUtils.post(
                    SerialPortConstant.PROJECT_URL, requestParams, "", JSONObject.class, "");
            return response.getBody();
        } catch (Exception e) {
            log.error("获取项目配置失败", e);
            return null;
        }
     }

    /**
     * 查找目标摄像头（IP地址末尾为2的摄像头）
     */
     private Map<String, String> findTargetCamera(JSONObject projectConfig, Map<String, String> authInfo, String macAddress) {
        try {
            String clientId = projectConfig.getJSONObject("client").getStr("id");
            JSONArray cameras = projectConfig.getJSONArray("cameras");

            for (int i = 0; i < cameras.size(); i++) {
                JSONObject camera = cameras.getJSONObject(i);
                String cameraIp = camera.getStr("ip");

                if (isTargetCamera(cameraIp)) {
                    // 构建返回结果
                    Map<String, String> result = new HashMap<>();
                    result.put("cameraId", camera.getStr("id"));
                    result.put("cameraSN", camera.getStr("sn"));
                    result.put("token", authInfo.get("token"));
                    result.put("clientId", macAddress);
                    result.put("id", clientId);
                    result.put("timestamp", authInfo.get("timestamp"));
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("查找目标摄像头失败", e);
        }
        return null;
     }

    /**
     * 判断是否为目标摄像头（IP地址末尾为2）
     */
     private boolean isTargetCamera(String cameraIp) {
        if (StringUtils.isBlank(cameraIp)) {
            return false;
        }

        try {
            String[] ipParts = cameraIp.split("\\.");
            if (ipParts.length >= 4) {
                String lastPart = ipParts[3]; // 获取IP最后一段
                String lastDigit = lastPart.substring(lastPart.length() - 1); // 获取最后一位数字
                return TARGET_CAMERA_IP_SUFFIX.equals(lastDigit);
            }
        } catch (Exception e) {
            log.error("解析摄像头IP失败: {}", cameraIp, e);
        }

        return false;
     }

    /**
     * MQTT连接成功后的回调
     * 可以在这里进行主题订阅等操作
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT连接成功，连接方式：{}", reconnect ? "重连" : "直连");

        // 获取设备信息
        Map<String, String> deviceInfo = getDeviceInfo();
        if (deviceInfo == null) {
            log.warn("设备信息为空，无法进行订阅");
            return;
        }

        String clientId = deviceInfo.get("id");
        String cameraSN = deviceInfo.get("cameraSN");

        if (StringUtils.isBlank(clientId) || StringUtils.isBlank(cameraSN)) {
            log.warn("订阅主题所需参数缺失：clientId={}，cameraSN={}", clientId, cameraSN);
            return;
        }

        try {
            mqttClientOne.subscribe(MqttConstants.TAKE_OFF + "_" + clientId, 2);
            mqttClientOne.subscribe(MqttConstants.REPLY_FRONT + cameraSN + MqttConstants.REPLY_LAST, 2);
            log.info("订阅主题成功：{}，{}",
                    MqttConstants.TAKE_OFF + "_" + clientId,
                    MqttConstants.REPLY_FRONT + cameraSN + MqttConstants.REPLY_LAST);
        } catch (Exception e) {
            log.error("订阅主题失败：{}", e.getMessage(), e);
        }
    }


    /**
     * 消息发送完成后的回调
     */
     @Override
     public void deliveryComplete(IMqttDeliveryToken deliveryToken) {
        log.debug("消息发送完成：{}", deliveryToken.isComplete());
     }
  }

package com.xiaodu.serialportdemo.service.impl;

import cn.hutool.jwt.JWT;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xiaodu.serialportdemo.constant.SerialPortConstant;
import com.xiaodu.serialportdemo.mapper.CarMapper;
import com.xiaodu.serialportdemo.mqtt.JwMqttClient;
import com.xiaodu.serialportdemo.service.CarService;
import com.xiaodu.serialportdemo.utils.RestTemplateUtils;
import com.xiaodu.serialportdemo.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.NetworkInterface;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName: CarServiceImpl
 * Package: com.serial.port.service.impl
 * Description:
 *
 * @Create 2025-01-18 16:31
 */
@Slf4j
@Service
public class CarServiceImpl implements CarService {
    @Autowired
    @Lazy
    private CarMapper carMapper;

    @Value("${JWT.client_id}")
    private String clientId;

//    @Autowired
//    @Lazy
//    private StringRedisTemplate redisTemplate;

    @Autowired
    @Lazy
    private JwMqttClient jwMqttClient;

    @Override
    public List<CarVo> selectByAllNumber(String numberStr) {
        return carMapper.selectByAllNumber(numberStr);
    }

    @Override
    public Map<String, String> getToken() {
        // 获取 MAC 地址
        String clientID = null;
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            for (NetworkInterface netInterface : Collections.list(interfaces)) {
                // 过滤掉 Wi-Fi、WLAN、回环网卡，仅获取有线网卡
                if (!netInterface.getDisplayName().contains("Wi-Fi")
                        && !netInterface.getDisplayName().contains("WLAN")
                        && !netInterface.isLoopback()
                        && netInterface.getHardwareAddress() != null) {
                    byte[] macAddress = netInterface.getHardwareAddress();
                    if (macAddress != null) {
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < macAddress.length; i++) {
                            sb.append(String.format("%02X%s", macAddress[i], (i < macAddress.length - 1) ? "-" : ""));
                        }
                        clientID = sb.toString();
                        log.info("获取到 MAC 地址：{}", clientID);
                        break; // 只取第一个符合条件的网卡
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取 MAC 地址失败", e);
        }

        // 获取或生成时间戳
//        String cachedTimestamp = redisTemplate.opsForValue().get(SerialPortConstant.CATCH_KEY.TIME_STAMP);
        long timestamp;
//        if (cachedTimestamp != null) {
//            timestamp = Long.parseLong(cachedTimestamp);
//        } else {
        timestamp = System.currentTimeMillis();
//            redisTemplate.opsForValue().set(SerialPortConstant.CATCH_KEY.TIME_STAMP, String.valueOf(timestamp), 10, TimeUnit.MINUTES);
//        }

        // 计算 MD5 以生成 Token
        String timestampMD5 = DigestUtils.md5Hex(String.valueOf(timestamp));
        String newClientId = DigestUtils.md5Hex(clientID);
        String key = SerialPortConstant.CLIENT_KEY + timestampMD5;
        byte[] keyBytes = key.getBytes();
        String token = JWT.create()
                .setPayload("auth_id", newClientId)
                .setPayload("timestamp", timestamp)
                .setKey(keyBytes)
                .sign();

        // 存入 Redis
//        redisTemplate.opsForValue().set(SerialPortConstant.CATCH_KEY.TOKEN, token, 10, TimeUnit.MINUTES);
//        redisTemplate.opsForValue().set(SerialPortConstant.CATCH_KEY.CLIENT_SN, newClientId, 10, TimeUnit.MINUTES);

        // 返回 Token 和时间戳
        Map<String, String> map = new HashMap<>();
        map.put("token", token);
        map.put("timestamp", String.valueOf(timestamp));
        return map;
    }


    @Override
    @Cacheable("projectId")
    public JSONObject getProjectId() {
        long retryInterval = 5000L; // 重试间隔 单位 毫秒
        while (true) {
            try {
                // 获取当前对象的token信息
                Map<String, String> map = this.getToken();
                // 发送POST请求获取项目信息
                ResponseEntity<JSONObject> response = RestTemplateUtils.post(
                        SerialPortConstant.PROJECT_URL, map, "", JSONObject.class, "");
                // 处理Json数据
                JSONObject body = response.getBody();
                if (body == null) {
                    throw new RuntimeException("获取项目信息失败");
                }
                // 获取 "client" JSON 对象
                JSONObject client = body.getJSONObject("client");
                if (client == null) {
                    throw new RuntimeException("获取项目信息失败");
                }
                // 提取 projectId 和 clientId
//                String projectId = client.getStr("projectId", "");
//                String clientId = client.getStr("id", "");
                // 存入 Redis
//                redisTemplate.opsForValue().set(SerialPortConstant.CATCH_KEY.PROJECT_ID, projectId, 10, TimeUnit.MINUTES);
//                redisTemplate.opsForValue().set(SerialPortConstant.CATCH_KEY.CLIENT_ID, clientId, 10, TimeUnit.MINUTES);
                // 返回原始 JSON 响应
//                System.out.println("body:"+body);
                return body;
            } catch (Exception e) {
                log.error("获取项目信息失败", e);
            }
            try {
                Thread.sleep(retryInterval);
            } catch (Exception e) {
                log.error("重试延迟时出现异常", e);
                break;
            }
        }
        return null;
    }


    @Override
    public List<String> getCarNumbers(String numberStr) {
        List<String> list = new ArrayList<>();
        // 获取当前对象的token信息，返回一个Map
        Map<String, String> map = this.getToken();
        // 发送 POST 请求获取车牌数据
        ResponseEntity<JSONObject> response = RestTemplateUtils.post(SerialPortConstant.CAR_NUMBERS_URL, map, "", JSONObject.class, "");
        JSONObject body = response.getBody();
        if (body == null) {
            throw new RuntimeException("获取车牌数据失败");
        }
        // 获取车牌号数组
        JSONArray carNumbersArray = body.getJSONArray("carNumbers");
        for (Object carNumber : carNumbersArray) {
            list.add(carNumber.toString());
        }
        // 模糊查询
        return list.stream()
                .filter(car -> numberStr == null || car.contains(numberStr)) // 模糊匹配
                .sorted(Comparator.reverseOrder()) // 按降序排序
                .collect(Collectors.toList());
    }

    @Override
    public boolean submitData(WeighingDataVo weighingDataVo) {
        try {
            // 获取当前对象的token信息
            Map<String, String> map = this.getToken();
            // 发送POST请求获取项目信息
            RestTemplateUtils.post(SerialPortConstant.SUBMIT_DATA_URL, map, weighingDataVo, JSONObject.class, "");
            return true;
        } catch (Exception e) {
            log.error("提交失败", e);
        }
        return false;
    }

    @Override
    public void downloadUpdate() {
            try {
                // 获取当前对象的token信息
                Map<String, String> map = this.getToken();
                // 发送POST请求获取项目信息
                RestTemplateUtils.post(SerialPortConstant.DOWNLOAD_URL, map, "", JSONObject.class, "");
            } catch (Exception e) {
                log.error("获取项目信息失败", e);
        }
    }

    @Override
    public void submitLiftRodData(LiftRodDataVo liftRodDataVo) {
        try {
            // 获取当前对象的token信息
            Map<String, String> map = this.getToken();
            // 发送POST请求获取项目信息
            RestTemplateUtils.post(SerialPortConstant.SUBMIT_LIFTRODDATA_URL, map, liftRodDataVo, JSONObject.class, "");
        } catch (Exception e) {
            log.error("获取项目信息失败", e);
        }
    }

    @Override
    public void liftRodAppeal(String causeAppeal) {
        try {
            LiftRodAppealVo liftRodAppealVo = new LiftRodAppealVo();
            // 获取当前对象的token信息
            Map<String, String> map = this.getToken();
            JSONObject project = getProjectId();
            if(project!=null){
                JSONObject client = project.getJSONObject("client");
                String projectId = client.getString("projectId");
                String projectName = client.getString("projectName");
                String stationId = client.getString("stationId");
                liftRodAppealVo.setProjectId(projectId);
                liftRodAppealVo.setProjectName(projectName);
                liftRodAppealVo.setCauseAppeal(causeAppeal);
                liftRodAppealVo.setStationId(stationId);
            }
            liftRodAppealVo.setTimeNumber(0);
            // 发送POST请求抬杆申诉
            RestTemplateUtils.post(SerialPortConstant.LIFTRODAPPEAL_URL, map, liftRodAppealVo, JSONObject.class, "");
        } catch (Exception e) {
            log.error("获取项目信息失败", e);
        }
    }

    @Override
    public String checkWeight(LiftCheckWeightQuery query) {
        try {
            // 获取当前对象的token信息
            Map<String, String> map = this.getToken();
            // 发送POST请求获取项目信息
            ResponseEntity<JSONObject> response = RestTemplateUtils.post(SerialPortConstant.CHECK_WEIGHT_URL, map, query, JSONObject.class, "");
            JSONObject body = response.getBody();
            if(body!=null){
                return body.getString("checked");
            }

        } catch (Exception e) {
            log.error("获取项目信息失败", e);
        }
        return null;
    }


}

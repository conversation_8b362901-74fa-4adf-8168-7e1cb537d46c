<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaodu.serialportdemo.mapper.SqLiteMapper">
    <!-- 按车牌号分页查询 -->
    <select id="selectPageByCarNumber" resultType="com.xiaodu.serialportdemo.vo.WeighingDataVo">
        SELECT create_time AS createTime,
               weight AS weight,
               car_number AS carNumber,
               data_type AS dataType
        FROM tempdatapool
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
          AND car_number LIKE #{carNumber} || '%'
        ORDER BY create_time DESC
    </select>

    <!-- 按创建时间分页查询 -->
    <select id="selectPageByCreateTime" resultType="com.xiaodu.serialportdemo.vo.WeighingDataVo">
        SELECT create_time AS createTime,
               weight AS weight,
               car_number AS carNumber,
               data_type AS dataType
        FROM tempdatapool
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY create_time DESC
    </select>

</mapper>

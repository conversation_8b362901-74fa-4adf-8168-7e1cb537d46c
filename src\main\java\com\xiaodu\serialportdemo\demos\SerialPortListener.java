package com.xiaodu.serialportdemo.demos;

/**
 * @Author: zt
 * @CreateTime: 2025-01-18
 * @Version: 1.0
 */

import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortDataListener;
import com.fazecast.jSerialComm.SerialPortEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * 串口监听
 */
@Slf4j
public class SerialPortListener implements SerialPortDataListener {

    private DataAvailableListener mDataAvailableListener;

    public SerialPortListener(DataAvailableListener mDataAvailableListener) {
        this.mDataAvailableListener = mDataAvailableListener;
    }

    @Override
    public int getListeningEvents() {//必须是return LISTENING_EVENT_DATA_AVAILABLE 这个才会开启串口工具的监听
        return SerialPort.LISTENING_EVENT_DATA_AVAILABLE | SerialPort.LISTENING_EVENT_PORT_DISCONNECTED;
    }

    @Override
    public void serialEvent(SerialPortEvent serialPortEvent) {
        try {
            if (serialPortEvent.getEventType() == SerialPort.LISTENING_EVENT_PORT_DISCONNECTED) {
                log.error("检测到串口断开事件: {}", serialPortEvent.getSerialPort().getSystemPortName());
                SerialPortManager.closePort(serialPortEvent.getSerialPort());
                // 通知数据监听器处理断开事件
                if (mDataAvailableListener != null) {
                    mDataAvailableListener.onPortDisconnected();
                }
                return;
            }
            
            if (serialPortEvent.getEventType() == SerialPort.LISTENING_EVENT_DATA_AVAILABLE) {
                if (mDataAvailableListener != null) {
                    mDataAvailableListener.dataAvailable();
                }
            }
        } catch (Exception e) {
            log.error("串口监听器处理事件时发生异常", e);
        }
    }
}

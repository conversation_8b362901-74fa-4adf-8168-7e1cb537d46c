package com.xiaodu.serialportdemo.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

@Configuration
public class SwaggerApi {
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30) //指定swagger3.0版本
                .enable(true) //是否开启swagger
                .select()
//                .apis(RequestHandlerSelectors.basePackage("com.xiaodu.serialportdemo.demos.web"))
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .build()
                .apiInfo(apiInfo());
    }

    private ApiInfo apiInfo(){
        return new ApiInfoBuilder()
                .title("一体机接口文档")
                .version("1.0")
                .description("一体机接口文档")
                .build();
    }

}

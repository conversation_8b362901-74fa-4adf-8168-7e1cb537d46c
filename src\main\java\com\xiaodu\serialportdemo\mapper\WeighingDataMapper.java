package com.xiaodu.serialportdemo.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaodu.serialportdemo.dto.TempDataPoolDo;
import com.xiaodu.serialportdemo.vo.WeighingDataVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * ClassName: WeighingDataMapper
 * Package: com.xiaodu.serialportdemo.mapper
 * Description:
 *
 * @Create 2025-01-18 18:09
 */
@Mapper
public interface WeighingDataMapper extends BaseMapper<TempDataPoolDo> {
    List<WeighingDataVo> getDataByCarNumber(@Param("offset") Integer offset,@Param("pageSize") Integer pageSize,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("carNumber") String carNumber);

    List<WeighingDataVo> getDataByCreateTime(@Param("offset") Integer offset,@Param("pageSize") Integer pageSize,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime);

    int getTotalCountByCarNumber(@Param("startTime") String startTime,
                                 @Param("endTime") String endTime,
                                 @Param("carNumber") String carNumber);

    int getTotalCountByCreateTime(@Param("startTime") String startTime,
                                  @Param("endTime") String endTime);

    List<TempDataPoolDo> selectDataByIsPull();

    void updateAll(@Param("tempDataPoolDoList") List<TempDataPoolDo> tempDataPoolDoList);

    WeighingDataVo getImageById(@Param("id") String id);
}

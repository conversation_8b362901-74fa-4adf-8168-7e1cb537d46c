//package com.xiaodu.serialportdemo.job;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import javax.annotation.PostConstruct;
//import java.io.BufferedReader;
//import java.io.InputStreamReader;
//import java.nio.charset.Charset;
//
//
//@Component
//public class RestartSerialPort {
//    private final Logger log = LoggerFactory.getLogger(RestartSerialPort.class);
//    private static boolean lastNetWorkStatus = true;
//    private static final String PING_ADDRESS = "www.baidu.com"; // 监测的地址
//    private static final String JAVA_SERVICE_CMD = "java -jar D:\\jar\\SerialPortDemo-0.0.1-SNAPSHOT.jar"; // 启动 Java 服务命令
//    private static Process javaProcess = null; // Java 服务进程
//
//    /**
//     * 在 Spring Boot 启动时自动启动 Java 服务
//     */
//    @PostConstruct
//    public void init() {
//        if (checkNetwork()) {
//            startJavaService();
//        } else {
//            System.out.println("无网络，暂不启动Java服务");
//        }
//    }
//
//    @Scheduled(fixedRate = 10000)
//    public void restartSerialPort() {
//        boolean isConnected = checkNetwork();
//
//        if (!lastNetWorkStatus && isConnected) {
//            startJavaService();
//        } else if (lastNetWorkStatus && !isConnected) {
//            stopJavaService();
//        }
//        lastNetWorkStatus = isConnected;
//    }
//
//    private boolean checkNetwork() {
//        try {
//            Process process = Runtime.getRuntime().exec("ping -n 1 " + PING_ADDRESS);
//            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), Charset.forName("GBK")));
//            String line;
//            while ((line = reader.readLine()) != null) {
//                if (line.contains("丢失 = 0 (0% 丢失)")) { // Windows 下 ping 成功返回 TTL=
//                    return true;
//                }
//            }
//        } catch (Exception e) {
//            log.error("网络连接异常", e);
//        }
//        return false;
//    }
//
//    /**
//     * 启动 Java 服务
//     */
//    private void startJavaService() {
//        try {
//            log.info("正在启动服务...");
//            javaProcess = Runtime.getRuntime().exec(JAVA_SERVICE_CMD);
//            log.info("服务启动成功");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 停止 Java 服务
//     */
//    private void stopJavaService() {
//        if (javaProcess != null) {
//            log.info("正在停止服务...");
//            javaProcess.destroy();
//            javaProcess = null;
//            log.info("服务停止成功");
//        } else {
//            log.info("服务未启动");
//        }
//    }
//
//    /**
//     * 重新启动 Java 服务
//     */
//    private void restartJavaService() {
//        startJavaService();
//        stopJavaService();
//    }
//}

package com.xiaodu.serialportdemo.dto;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("tempdatapool")
public class TempDataPoolDo implements Serializable {

    @TableId(value = "id",type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String createTime;

    @TableField("car_number")
    private String carNumber;

    @TableField("weight")
    private BigDecimal weight;

    @TableField("file_list")
    private JSON fileList;

    @TableField("data_type")
    private String dataType;

    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String updateTime;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("is_pull")
    private Integer isPull;

    @TableField("head_image")
    private String headImage; //车前照片

    @TableField("rear_image")
    private String rearImage;//车后照片
}

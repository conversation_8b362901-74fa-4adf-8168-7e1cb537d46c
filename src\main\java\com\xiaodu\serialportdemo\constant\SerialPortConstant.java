package com.xiaodu.serialportdemo.constant;

import lombok.Data;

/**
 * 常量类
 */
@Data
public class SerialPortConstant {
    //密钥Key
    public static final String CLIENT_KEY = "adfsfsfsfsdf";

    //请求项目信息的Url
    public static final String PROJECT_URL = "https://smg.edgd.top/api/client/client/getClientConfig";
    //本地
    public static final String LOCAL_PROJECT_URL = "http://127.0.0.1:8080/api/client/client/getClientConfig";
    //请求华为云获取项目信息
    public static final String HUAWEI_PROJECT_URL = "https://test.edgd.top:7333/api/client/client/getClientConfig";
    //猿本科技后台项目信息URL
    public static final String YUAN_BEN_PROJECT_URL = "https://smmtest.yuanbenkeji.com/api/client/client/getClientConfig";

    // ------------------------------------------------------------------------------------------------------------------

    //请求车牌号信息的Url
    public static final String CAR_NUMBERS_URL = "https://smg.edgd.top/api/client/client/carNumbers";
    //请求本地车牌号信息
    public static final String LOCAL_CAR_NUMBERS_URL = "http://127.0.0.1:8080/api/client/client/carNumbers";
    //请求华为云车牌号信息的Url
    public static final String HUAWEI_CAR_NUMBERS_URL = "https://test.edgd.top:7333/api/client/client/carNumbers";
    //猿本科技后台车牌号信息URL
    public static final String YUAN_BEN_CAR_NUMBERS_URL = "https://smmtest.yuanbenkeji.com/api/client/client/carNumbers";

    // ------------------------------------------------------------------------------------------------------------------

    //请求客户端提交数据Url
    public static final String SUBMIT_DATA_URL = "https://smg.edgd.top/api/client/tempDataPool/save";
    //请求本地客户端提交数据Url
    public static final String LOCAL_SUBMIT_DATA_URL = "http://127.0.0.1:8080/api/client/tempDataPool/save";
    //请求华为云客户端提交数据Url
    public static final String HUAWEI_SUBMIT_DATA_URL = "https://test.edgd.top:7333/api/client/tempDataPool/save";

    // ------------------------------------------------------------------------------------------------------------------

    //请求下载更新包接口
    public static final String DOWNLOAD_URL = "https://smg.edgd.top/api/client/client//updateVersion/{version}";
    //请求本地下载更新包接口
    public static final String LOCAL_DOWNLOAD_URL = "http://127.0.0.1:8080/api/client/client//updateVersion/{version}";
    //请求华为云下载更新包接口
    public static final String HUAWEI_DOWNLOAD_URL = "https://test.edgd.top:7333/api/client/client//updateVersion/{version}";

    //------------------------------------------------------------------------------------------------------------------
    //请求客户端提交抬杆数据Url
    public static final String SUBMIT_LIFTRODDATA_URL = "https://smg.edgd.top/api/client/tempDataPool/submitLiftRodData";
    //请求本地客户端提交抬杆数据Url
    public static final String LOCAL_SUBMIT_LIFTRODDATA_URL = "http://127.0.0.1:8080/api/client/tempDataPool/submitLiftRodData";
    //请求华为云客户端提交抬杆数据Url
    public static final String HUAWEI_SUBMIT_LIFTRODDATA_URL = "https://test.edgd.top:7333/api/client/tempDataPool/submitLiftRodData";

    //------------------------------------------------------------------------------------------------------------------
    //请求客户端抬杆申诉Url
    public static final String LIFTRODAPPEAL_URL = "https://smg.edgd.top/api/client/tempDataPool/submitLiftRodData";
    //请求本地客户端抬杆申诉Url
    public static final String LOCAL_LIFTRODAPPEAL_URL = "http://127.0.0.1:8080/api/client/tempDataPool/submitLiftRodData";
    //请求华为云客户端抬杆申诉Url
    public static final String HUAWEI_LIFTRODAPPEAL_URL = "https://test.edgd.top:7333/api/client/tempDataPool/submitLiftRodData";

    //------------------------------------------------------------------------------------------------------------------
    //请求客户端校验出入场Url
    public static final String CHECK_WEIGHT_URL = "https://smg.edgd.top/api/client/tempDataPool/checkWeight";
    //请求本地客户端校验出入场Url
    public static final String LOCAL_CHECK_WEIGHT_URL = "http://127.0.0.1:8080/api/client/tempDataPool/checkWeight";
    //请求华为云客户端校验出入场Url
    public static final String HUAWEI_CHECK_WEIGHT_URL = "https://test.edgd.top:7333/api/client/tempDataPool/checkWeight";
    /**
     * Redis缓存Key
     */
    public static class CATCH_KEY{
        /**
         * token
         */
        public static final String TOKEN = "token";

        /**
         * 时间戳
         */
        public static final String TIME_STAMP = "client:token:timestamp";

        /**
         * 一体机SN码
         */
        public static final String CLIENT_SN = "clientSN";

        /**
         * 项目id
         */
        public static final String PROJECT_ID = "projectId";

        /**
         * 一体机ID
         */

        public static final String CLIENT_ID = "clientId";
    }

    /**
     * MQTT配置
     */
    public static class MQTT_CONF{
        /**
         * mqtt地址
         */
        public static final String MQTT_HOST = "tcp://*************:1883";

        public static final String LOCAL_MQTT_HOST = "tcp://127.0.0.1:1883";

        public static final String HUAWEI_MQTT_HOST = "tcp://*************:1894";

        public static final String YUAN_BEN_MQTT_HOST = "tcp://************:1883";

        /**
         * clientId前缀
         */
        public static final String MQTT_CLIENT_ID_PREFIX = "client_mqtt_";
    }
}

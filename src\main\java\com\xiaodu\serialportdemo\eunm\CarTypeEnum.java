package com.xiaodu.serialportdemo.eunm;

import java.util.Objects;

/**
 * ClassName: CarEnum
 * Package: com.ybkj.smm.modules.car.eunm
 * Description: 车辆类型枚举
 *
 * @Create 2024/12/21 8:44
 */

public enum CarTypeEnum {
    CAR_TYPE_IN("in", "进站"),CAR_TYPE_OUT("out", "出站");
    private String key;
    private String value;


    CarTypeEnum(){

    }
    CarTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }
    public static String getNameByValue(String key) {
        for (CarTypeEnum carTypeEnum : values()) {
            if (Objects.equals(carTypeEnum.getKey(), key)) {
                return carTypeEnum.getValue();
            }
        }
        return null;  // 如果没有找到对应的value，返回null
    }
    public String getValue() {
        return value;
    }

    public String setKey(String key) {
        return this.key = key;
    }

    public String setValue(String value) {
        return this.value = value;
    }

}

package com.xiaodu.serialportdemo.eunm;

/**
 * ClassName: CarStatusEnum
 * Package: com.ybkj.smm.modules.car.eunm
 * Description: 车辆状态枚举
 *
 * @Create 2024/12/21 10:56
 */
public enum CarStatusEnum {
    CAR_STATUS_UP(1,"未上传"),CAR_STATUS_DOWN(2,"已上传");
    private int key;
    private String value;


    CarStatusEnum(){

    }
    CarStatusEnum(int key, String value) {
        this.key = key;
        this.value = value;
    }

    public int getKey() {
        return key;
    }
//    根据Key获取Value
    public static String getNameByValue(int key) {
        for (CarStatusEnum carStatusEnum : values()) {
            if (carStatusEnum.getKey() == key) {
                return carStatusEnum.getValue();
            }
        }
        return null;  // 如果没有找到对应的value，返回null
    }
    public String getValue() {
        return value;
    }

    public int setKey(int key) {
        return this.key = key;
    }

    public String setValue(String value) {
        return this.value = value;
    }
}

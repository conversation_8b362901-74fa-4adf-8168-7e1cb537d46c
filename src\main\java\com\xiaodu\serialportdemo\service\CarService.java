package com.xiaodu.serialportdemo.service;

import com.alibaba.fastjson.JSONObject;
import com.xiaodu.serialportdemo.vo.CarVo;
import com.xiaodu.serialportdemo.vo.LiftCheckWeightQuery;
import com.xiaodu.serialportdemo.vo.LiftRodDataVo;
import com.xiaodu.serialportdemo.vo.WeighingDataVo;
import java.util.List;
import java.util.Map;


/**
 * ClassName: CarService
 * Package: com.serial.port.service
 * Description:
 *
 * @Create 2025-01-18 16:27
 */
public interface CarService {
    List<CarVo> selectByAllNumber(String numberStr);

    Map<String, String> getToken();

    JSONObject getProjectId();

    List<String> getCarNumbers(String numberStr);

    boolean submitData(WeighingDataVo weighingDataVo);

    void downloadUpdate();

    void submitLiftRodData(LiftRodDataVo liftRodDataVo);

    void liftRodAppeal(String causeAppeal);

    String checkWeight(LiftCheckWeightQuery query);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaodu.serialportdemo.mapper.CarMapper">

    <select id="selectByAllNumber" resultType="com.xiaodu.serialportdemo.vo.CarVo">
        select DISTINCT car_number as numberStr  from tempdatapool
        <where>
            <if test="numberStr!= null and numberStr != ''">
                and car_number LIKE #{numberStr} || '%'
            </if>
        </where>
        limit 6
    </select>
</mapper>

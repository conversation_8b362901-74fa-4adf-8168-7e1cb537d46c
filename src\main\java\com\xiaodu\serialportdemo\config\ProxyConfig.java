package com.xiaodu.serialportdemo.config;

import com.google.common.collect.ImmutableMap;
import org.mitre.dsmiley.httpproxy.ProxyServlet;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Servlet;
import java.util.Map;

/**
 * 后台处理反向代理
 */
@Configuration
public class ProxyConfig {
    @Value("${proxy.servlet_url}")
    private String servletUrl;
    @Value("${proxy.target_url}")
    private String targetUrl;

    @Bean
    public Servlet createProxyServlet() {
        return new ProxyServlet();
    }

    @Bean
    public ServletRegistrationBean proxyServletRegistration() {
        ServletRegistrationBean registrationBean = new ServletRegistrationBean(createProxyServlet(), servletUrl);
//        设置网址以及参数
        // 创建一个不可变的Map，用于存储Servlet的初始化参数
        Map<String, String> params = ImmutableMap.of(
                // 设置目标URI参数，值为targetUrl变量
                ProxyServlet.P_TARGET_URI, targetUrl,
                // 设置连接超时参数，值为20000毫秒（20秒）
                ProxyServlet.P_CONNECTTIMEOUT, "20000",
                // 设置读取超时参数，值为20000毫秒（20秒）
                ProxyServlet.P_READTIMEOUT, "20000",
                // 设置连接请求超时参数，值为20000毫秒（20秒）
                ProxyServlet.P_CONNECTIONREQUESTTIMEOUT, "20000",
                // 设置日志参数，值为false，表示不记录日志
                "log", "false");

        // 将创建的参数Map设置到registrationBean中
        registrationBean.setInitParameters(params);

        // 返回配置好的registrationBean对象
        return registrationBean;
    }
}

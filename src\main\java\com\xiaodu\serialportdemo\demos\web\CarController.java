package com.xiaodu.serialportdemo.demos.web;

import cn.hutool.core.date.DateUtil;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xiaodu.serialportdemo.dto.TempDataPoolDo;
import com.xiaodu.serialportdemo.mapper.SqLiteMapper;
import com.xiaodu.serialportdemo.mapper.WeighingDataMapper;
import com.xiaodu.serialportdemo.service.CarService;
import com.xiaodu.serialportdemo.service.WeighingDataService;
import com.xiaodu.serialportdemo.utils.Result;
import com.xiaodu.serialportdemo.vo.LiftCheckWeightQuery;
import com.xiaodu.serialportdemo.vo.LiftRodDataVo;
import com.xiaodu.serialportdemo.vo.WeighingDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * ClassName: CarController
 * Package: com.serial.port.api
 * Description:
 *
 * @Create 2025-01-18 16:25
 */
@RestController
@RequestMapping("/car")
@Api(tags = "一体机其他接口")
@Slf4j
public class CarController {
    //    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    @Lazy
    private CarService carService;
    @Autowired
    @Lazy
    private WeighingDataService weighingDataService;
//    private ListOperations<String, String> listOps;

    @Autowired
    @Lazy
    private WeighingDataMapper weighingDataMapper;

    @Autowired
    @Lazy
    private SqLiteMapper mapper;

//    @GetMapping("/getCarNumById")
//    public List<CarVo> getCarNumByAllNumber(String numberStr) {
//        return carService.selectByAllNumber(numberStr);
//    }

    @GetMapping("/getCarNumById")
    @ApiOperation("模糊查询车牌号接口")
    public List<String> getCarNumbers(String numberStr) {
        return carService.getCarNumbers(numberStr);
    }

    @GetMapping("/getDataByCarNumber")
    @ApiOperation("过磅数据车牌号模糊查询接口")
    public Map<String, Object> getDataByCarNumber(Integer pageNumber,
                                                  Integer pageSize,
                                                  @RequestParam(required = false) String carNumber,
                                                  @RequestParam(required = false) String startTime,
                                                  @RequestParam(required = false) String endTime) {
        return weighingDataService.getDataByCarNumber(pageNumber, pageSize, carNumber,startTime,endTime);
    }

    @GetMapping("/getImageById")
    @ApiOperation("根据id获取图片接口")
    public Result<WeighingDataVo> getImageById(String id) {
        return Result.ok(weighingDataService.getImageById(id));
    }

    /**
     * 获取token和时间戳
     *
     * @return
     */
    public Map<String, String> getToken() {
        return carService.getToken();
    }


    /**
     * 调用后台接口获取mqtt订阅消息所需参数
     *
     * @return
     */
    @GetMapping("/getProjectId")
    @ApiOperation("获取项目信息接口")
    public JSONObject getProjectId() {
        return carService.getProjectId();
    }

    /**
     * 插入图片到数据库接口
     * @param weighingDataVo
     */
    @PostMapping("/insert")
    @ApiOperation("新增过磅数据接口")
    public Result insert(@RequestBody WeighingDataVo weighingDataVo) {
        String prefix = "data:image/png;base64,";
        String head = weighingDataVo.getHeadImage();
        String newHead = prefix + head;
        String rear = weighingDataVo.getRearImage();
        String newRear = prefix + rear;
        String dataType = weighingDataVo.getDataType();
        String carNumber = weighingDataVo.getCarNumber();
        BigDecimal weight = weighingDataVo.getWeight();
        TempDataPoolDo tempDataPoolDo = new TempDataPoolDo();
        String now = DateUtil.now();
        tempDataPoolDo.setCreateTime(now);
        tempDataPoolDo.setUpdateTime(now);
        tempDataPoolDo.setHeadImage(newHead);
        tempDataPoolDo.setRearImage(newRear);
        tempDataPoolDo.setDataType(dataType);
        tempDataPoolDo.setCarNumber(carNumber);
        tempDataPoolDo.setWeight(weight);
//        tempDataPoolDo.setIsPull(1);
        tempDataPoolDo.setIsPull(weighingDataVo.getIsPull());
        int insert = mapper.insert(tempDataPoolDo);
        if (insert == 0) {
            return Result.build(null, Result.ResultCodeEnum.FAIL);
        }
        return Result.ok();
    }

    /**
     * 客户端提交数据接口
     */
    @PostMapping("/submitData")
    @ApiOperation("客户端提交数据接口")
    public Result submitData(@RequestBody WeighingDataVo weighingDataVo) {

        List<String> list = new ArrayList<>();
        if (weighingDataVo == null || weighingDataVo.getWeight() == null
                || weighingDataVo.getWeight().compareTo(BigDecimal.ZERO) <= 0
                || StringUtils.isBlank(weighingDataVo.getCarNumber())
                || StringUtils.isBlank(weighingDataVo.getHeadImage())
                || StringUtils.isBlank(weighingDataVo.getRearImage())
                || StringUtils.isBlank(weighingDataVo.getDataType())) {
            return Result.build(null, Result.ResultCodeEnum.NOTDATA);
        }
        list.add(weighingDataVo.getHeadImage());
        list.add(weighingDataVo.getRearImage());
        weighingDataVo.setFileList(list);
        // 提交数据到后台
        boolean isPull = carService.submitData(weighingDataVo);

        if (!isPull) {
            weighingDataVo.setIsPull(2);
            this.insert(weighingDataVo);
            return Result.build(isPull, Result.ResultCodeEnum.WARN);
        } else {
            weighingDataVo.setIsPull(1);
            this.insert(weighingDataVo);
            // 仅提交成功时，更新数据状态
            List<TempDataPoolDo> tempDataPoolDoList = weighingDataMapper.selectDataByIsPull();
            tempDataPoolDoList.forEach(
                    tempDataPoolDo -> {
                        tempDataPoolDo.setIsPull(1);
                        weighingDataMapper.updateById(tempDataPoolDo);
                    }
            );

            //重新提交数据
            tempDataPoolDoList.forEach(
                    tempDataPoolDo -> {
                        WeighingDataVo dataVo = new WeighingDataVo();
                        List<String> fileList = new ArrayList<>();
                        if (tempDataPoolDo.getHeadImage().contains("png")) {
                            tempDataPoolDo.setHeadImage(convertPngToJpg(tempDataPoolDo.getHeadImage()));
                        }
                        if (tempDataPoolDo.getRearImage().contains("png")) {
                            tempDataPoolDo.setRearImage(convertPngToJpg(tempDataPoolDo.getRearImage()));
                        }
                        fileList.add(tempDataPoolDo.getHeadImage());
                        fileList.add(tempDataPoolDo.getRearImage());
                        dataVo.setFileList(fileList);
                        dataVo.setCarNumber(tempDataPoolDo.getCarNumber());
                        dataVo.setDataType(tempDataPoolDo.getDataType());
                        dataVo.setWeight(tempDataPoolDo.getWeight());
                        carService.submitData(dataVo);
                    }
            );
        }
        return Result.ok(isPull);
    }

    public String convertPngToJpg(String base64Png) {
        try {
            // 解码 Base64 数据
            byte[] imageBytes = Base64.getDecoder().decode(base64Png.split(",")[1]);
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);

            // 读取 PNG 图片
            BufferedImage bufferedImage = ImageIO.read(bis);

            // 转换为 JPG 格式
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "jpg", bos);

            // 生成 Base64 数据
            byte[] jpgBytes = bos.toByteArray();
            return "data:image/jpg;base64," + Base64.getEncoder().encodeToString(jpgBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 下载更新包接口
     */
    @GetMapping("/downloadUpdate")
    @ApiOperation("下载更新包接口")
    public Result downloadUpdate() {
        carService.downloadUpdate();
        return Result.ok();
    }

    /**
     * 提交抬杆数据
     */
    @PostMapping("/submitLiftRodData")
    @ApiOperation("提交抬杆数据")
    public Result submitLiftRodData(@RequestBody LiftRodDataVo liftRodDataVo) {
        carService.submitLiftRodData(liftRodDataVo);
        return Result.ok();
    }

    /**
     * 抬杆申诉接口
     */
    @PostMapping("/liftRodAppeal")
    @ApiOperation("抬杆申诉接口")
    public Result liftRodAppeal(String causeAppeal) {
        carService.liftRodAppeal(causeAppeal);
        return Result.ok();
    }

    /**
     * 校验出入场
     */
    @PostMapping("/checkWeight")
    @ApiOperation("校验出入场")
    public Result checkWeight(@RequestBody LiftCheckWeightQuery query) {
        String checked = carService.checkWeight(query);
        return Result.ok(checked);
    }
}

# Spring应用配置
spring:
  # 应用名称
  application:
    name: serialport
  # 激活的配置文件
  profiles:
    active: prod

#  main:
#    allow-circular-references: true #允许spring依赖循环
  autoconfigure:
    exclude: com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration #排除pagehelper自动配置

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# MyBatis-Plus配置
mybatis-plus:
  # Mapper文件位置
  mapper-locations: classpath:/mapper/*.xml
  # 实体类扫描包路径
  typeAliasesPackage: com.xiaodu.serialportdemo.vo
  # 全局配置
  global-config:
    # 是否刷新Mapper，用于测试
    refresh-mapper: true
    # 数据库配置
    db-config:
      # 主键类型，ASSIGN_UUID表示使用UUID作为主键
      id-type: ASSIGN_UUID
      # 逻辑删除字段名称
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  # MyBatis配置
#  configuration:
#    # 是否开启下划线转驼峰命名
#    map-underscore-to-camel-case: true
#    # 是否开启缓存
#    cache-enabled: false
#    # 是否调用setter方法对null值进行处理
#    call-setters-on-nulls: true
#  # 类型处理器包路径
#  type-handlers-package: com.serial.port.*.handler
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启 MyBatis SQL 日志

#自定义代理相关配置
#代理的本地路由
proxy:
  servlet_url: /sandMiningProxy/*
  target_url: https://smg.edgd.top

# GPIO道闸控制配置
gpio:
  # 基础配置
  delay: 500          # GPIO信号延时(ms)，建议100-500ms
  output-value: 1     # GPIO输出值，1=高电平，0=低电平
  # IO端口映射
  io-mapping:
    raise: 0          # 抬杆IO端口
    drop: 1           # 落杆IO端口
  # 高级配置
  timeout: 3000       # GPIO操作超时时间(ms)
  retry-count: 3      # 失败重试次数
  
  # 道闸运行参数
  barrier:
    raise-time: 3000    # 抬杆完成时间(ms)
    drop-time: 2000     # 落杆完成时间(ms)
    hold-time: 30000    # 最大保持时间(ms)
    force-level: 2      # 力度等级 1-3 (1=轻, 2=中, 3=重)
    speed-level: 2      # 速度等级 1-3 (1=慢, 2=中, 3=快)
    
  # 安全保护参数  
  safety:
    obstacle-detect: true    # 启用障碍物检测
    auto-reverse: true       # 遇阻自动反转
    emergency-stop: true     # 紧急停止功能
    max-force: 80           # 最大输出力度(%)
    pressure-limit: 50       # 压力保护阈值(%)
    
  # 信号控制参数
  signal:
    pulse-width: 200        # 脉冲宽度(ms)
    pulse-interval: 100     # 脉冲间隔(ms)
    signal-level: 24        # 信号电压(V): 12V/24V
    drive-current: 500      # 驱动电流(mA)
    brake-enable: true      # 启用电磁制动
    
  # 状态监控参数
  monitor:
    position-feedback: true  # 位置反馈
    current-monitor: true    # 电流监控
    temperature-monitor: true # 温度监控
    status-report-interval: 1000  # 状态上报间隔(ms)
    heartbeat-interval: 5000      # 心跳间隔(ms)
    
  # 环境适配参数
  environment:
    temperature-range: "-20~60"  # 工作温度范围(°C)
    humidity-max: 85            # 最大湿度(%)
    wind-resistance: 6          # 抗风等级
    waterproof-level: "IP54"    # 防护等级
    
  # 维护参数
  maintenance:
    auto-lubrication: false     # 自动润滑
    cycle-count-alarm: 100000   # 循环次数报警阈值
    maintenance-reminder: 30    # 维护提醒周期(天)
    error-code-report: true     # 错误代码上报

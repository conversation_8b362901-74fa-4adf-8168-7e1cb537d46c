# Spring应用配置
spring:
  # 应用名称
  application:
    name: serialport
  # 激活的配置文件
  profiles:
    active: prod

#  main:
#    allow-circular-references: true #允许spring依赖循环
  autoconfigure:
    exclude: com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration #排除pagehelper自动配置

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# MyBatis-Plus配置
mybatis-plus:
  # Mapper文件位置
  mapper-locations: classpath:/mapper/*.xml
  # 实体类扫描包路径
  typeAliasesPackage: com.xiaodu.serialportdemo.vo
  # 全局配置
  global-config:
    # 是否刷新Mapper，用于测试
    refresh-mapper: true
    # 数据库配置
    db-config:
      # 主键类型，ASSIGN_UUID表示使用UUID作为主键
      id-type: ASSIGN_UUID
      # 逻辑删除字段名称
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
  # MyBatis配置
#  configuration:
#    # 是否开启下划线转驼峰命名
#    map-underscore-to-camel-case: true
#    # 是否开启缓存
#    cache-enabled: false
#    # 是否调用setter方法对null值进行处理
#    call-setters-on-nulls: true
#  # 类型处理器包路径
#  type-handlers-package: com.serial.port.*.handler
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启 MyBatis SQL 日志

#自定义代理相关配置
#代理的本地路由
proxy:
  servlet_url: /sandMiningProxy/*
  target_url: https://smg.edgd.top

package com.xiaodu.serialportdemo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaodu.serialportdemo.dto.TempDataPoolDo;
import com.xiaodu.serialportdemo.vo.WeighingDataVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface SqLiteMapper extends BaseMapper<TempDataPoolDo> {
    // 按车牌号分页查询
    IPage<WeighingDataVo> selectPageByCarNumber(Page<WeighingDataVo> page,
                                                @Param("startTime") Date startTime,
                                                @Param("endTime") Date endTime,
                                                @Param("carNumber") String carNumber);

    // 按创建时间分页查询
    IPage<WeighingDataVo> selectPageByCreateTime(Page<WeighingDataVo> page,
                                                 @Param("startTime") Date startTime,
                                                 @Param("endTime") Date endTime);
}

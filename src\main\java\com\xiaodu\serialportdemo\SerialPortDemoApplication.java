package com.xiaodu.serialportdemo;

import com.feiniaojin.gracefulresponse.EnableGracefulResponse;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.oas.annotations.EnableOpenApi;

@SpringBootApplication
// 使用@EnableGracefulResponse注解
// 该注解通常用于Spring Boot应用中，用于启用优雅的响应机制
// 优雅的响应机制可能包括在应用关闭时处理未完成的请求、
// 提供友好的错误响应等，具体功能取决于注解的实现细节
//@EnableGracefulResponse
//@EnableScheduling
@EnableOpenApi  //使用@EnableOpenApi注解启用OpenAPI功能
public class SerialPortDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(SerialPortDemoApplication.class, args);
    }

}

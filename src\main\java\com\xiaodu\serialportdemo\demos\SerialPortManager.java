package com.xiaodu.serialportdemo.demos;

/**
 * @Author: zt
 * @CreateTime: 2025-01-18
 * @Version: 1.0
 */

import com.fazecast.jSerialComm.SerialPort;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class SerialPortManager {
    //查找所有可用端口
    public static List<String> findPorts() {
        // 获得当前所有可用串口
        SerialPort[] serialPorts = SerialPort.getCommPorts();
        List<String> portNameList = new ArrayList<String>();
        // 将可用串口名添加到List并返回该List
        for (SerialPort serialPort : serialPorts) {
            portNameList.add(serialPort.getSystemPortName());
        }
        //去重
        portNameList = portNameList.stream().distinct().collect(Collectors.toList());
        return portNameList;
    }

    /**
     * 打开串口
     *
     * @param portName 端口名称
     * @param baudRate 波特率
     * @return 串口对象
     */
    public static SerialPort openPort(String portName, Integer baudRate, Integer dataBit, Integer stopBit, Integer verifyBit) {
        SerialPort serialPort = SerialPort.getCommPort(portName);

        // 如果串口已经打开，先关闭它以确保配置正确
        if (serialPort.isOpen()) {
            log.info("串口{}已打开，先关闭以重新配置", portName);
            serialPort.closePort();
        }
        // 打开串口
        boolean openResult = serialPort.openPort();

        if (!openResult) {
            log.error("串口{}打开失败", portName);
            return null;
        }

        // 设置串口参数
        serialPort.setFlowControl(SerialPort.FLOW_CONTROL_DISABLED);
        serialPort.setComPortParameters(baudRate, dataBit, verifyBit, stopBit);
        
        // 优化超时设置：使用非阻塞模式，避免读取阻塞
        // 地磅数据通常是持续发送的，不需要长时间阻塞等待
        serialPort.setComPortTimeouts(SerialPort.TIMEOUT_NONBLOCKING, 0, 0);

        log.info("串口{}打开成功", portName);

        return serialPort;
    }


    /**
     * 关闭串口
     *
     * @param serialPort 待关闭的串口对象
     */
    public static void closePort(SerialPort serialPort) {
        if (serialPort != null && serialPort.isOpen()) {
            try {
                // 移除监听器
                serialPort.removeDataListener();
                log.info("移除监听器成功");

                // 关闭串口
                serialPort.closePort();
                log.info("串口关闭成功");

            } catch (Exception e) {
                log.error("关闭串口失败", e);
            }
        } else {
            log.warn("串口已关闭或为null");
        }
    }

    /**
     * 往串口发送数据
     *
     * @param serialPort 串口对象
     * @param content    待发送数据
     */
    public static void sendToPort(SerialPort serialPort, byte[] content) {
        if (!serialPort.isOpen()) {
            return;
        }
        serialPort.writeBytes(content, content.length);
    }

    /**
     * 从串口读取数据
     *
     * @param serialPort 当前已建立连接的SerialPort对象
     * @return 读取到的数据
     */
    public static byte[] readFromPort(SerialPort serialPort) {
        byte[] resultData = null;
        try {
            // 增加null检查，防止NPE
            if (serialPort == null || !serialPort.isOpen()) {
                return null;
            }

            // 等待数据可用，最多等待50ms
            int waitCount = 0;
            while (serialPort.bytesAvailable() <= 0 && waitCount < 5) {
                Thread.sleep(10);
                waitCount++;
            }

            int availableBytes = serialPort.bytesAvailable();
            if (availableBytes > 0) {
                // 创建合适大小的缓冲区
                byte[] readBuffer = new byte[availableBytes];
                int numRead = serialPort.readBytes(readBuffer, readBuffer.length);
                
                if (numRead > 0) {
                    // 如果读取的数据少于缓冲区大小，创建精确大小的数组
                    if (numRead < readBuffer.length) {
                        resultData = new byte[numRead];
                        System.arraycopy(readBuffer, 0, resultData, 0, numRead);
                    } else {
                        resultData = readBuffer;
                    }
                    // 成功读取数据
                } else {
                    log.warn("读取数据失败，预期{}字节，实际{}字节", availableBytes, numRead);
                }
            }
        } catch (InterruptedException e) {
            log.error("读取串口数据被中断", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("读取串口数据时发生异常", e);
        }
        return resultData;
    }
    
    /**
     * 检查串口是否健康
     *
     * @param serialPort 串口对象
     * @return true=健康，false=异常
     */
    public static boolean isPortHealthy(SerialPort serialPort) {
        try {
            if (serialPort == null) {
                return false;
            }
            return serialPort.isOpen();
        } catch (Exception e) {
            log.error("检查串口健康状态异常", e);
            return false;
        }
    }

    /**
     * 添加监听器
     *
     * @param serialPort 串口对象
     * @param listener   串口存在有效数据监听
     */
    public static void addListener(SerialPort serialPort, DataAvailableListener listener) {
        try {
            // 给串口添加监听器
            serialPort.addDataListener(new SerialPortListener(listener));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

package com.xiaodu.serialportdemo.mqtt;

import com.xiaodu.serialportdemo.vo.MessageVo;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


/**
 * ClassName: MessageStgorage
 * Package: com.ybkj.smm.modules.mqtty
 * Description:
 *
 * @Create 2024/12/26 15:37
 */
//暂存MQTT消息类
public class MessageStorage {

    private static volatile MessageVo message;
    private static final ScheduledExecutorService scheduler= Executors.newScheduledThreadPool(1);

    public static synchronized void saveRecognitionMessage(MessageVo messageVo){
        message = messageVo;
        scheduleMessageClear(()->message=null,600);
    }
    public static synchronized MessageVo getMessage(){
        return message;
    }
//    定时任务清空消息方法
    private static void scheduleMessageClear(Runnable task,int delayInSeconds){
        scheduler.schedule(task,delayInSeconds, TimeUnit.SECONDS);
    }
//    关闭线程池
    public static void shutdownScheduler(){
        scheduler.shutdown();
    }
}

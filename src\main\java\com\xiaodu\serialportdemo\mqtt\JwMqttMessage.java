package com.xiaodu.serialportdemo.mqtt;


public class JwMqttMessage {
    private String topic;
    private String content;
    private String time;

    public String getTopic() {
        return topic;
    }
    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getContent() {
        return content;
    }
    public void setContent(String content) {
        this.content = content;
    }

    public String getTime() {
        return time;
    }
    public void setTime(String time) {
        this.time = time;
    }
    @Override
    public String toString(){
        return "JsonStr";
        //        return JSON.toJSONString(this);
    }
}

# 配置服务器相关设置
server:
  # 设置服务器监听的端口号
  port: 8077

## 配置Spring相关设置
#spring:
#  # 配置数据源相关设置
#  datasource:
#    # 数据库连接URL，指定数据库类型、地址、端口、数据库名称及连接参数
#      url: jdbc:sqlite:C:\Users\<USER>\Desktop\platform.db
#    #    url: *********************************************************************************************************************************************************************************
#    #    # 数据库用户名
#    #    username: root
#    #    # 数据库密码
#    #    password: root
#    #    # 初始化连接池的连接数量
#    #    initial-size: 10
#    #    # 连接池中最大的活跃连接数量
#    #    max-active: 50
#    #    # 连接池中最小的空闲连接数量
#    #    min-idle: 10
#    #    # 连接池中连接的最大等待时间（毫秒）
#    #    max-wait: 60000
#    #    # 是否池化准备语句
#    #    pool-prepared-statements: true
#    #    # 每个连接池最大的准备语句数量
#    #    max-pool-prepared-statement-per-connection-size: 20
#    # 数据库驱动类名
#      driver-class-name: org.sqlite.JDBC
spring:
  datasource:
    url: ********************************************************
    driver-class-name: org.sqlite.JDBC
#  redis:
#    port: 6379
#    host: 127.0.0.1
#    #    password: #默认没有密码
#    database: 0
  # 配置Spring MVC相关的设置
  mvc:
    # 配置路径匹配策略
    pathmatch:
      # 设置路径匹配策略为Ant风格的路径匹配器
      matching-strategy: ant_path_matcher
# 配置日志相关设置
logging:
  # 设置日志级别
  level:
    # 设置com.serial.port包的日志级别为debug
    com.xiaodu.serialportdemo: debug
  config: classpath:logback-spring.xml

JWT:
#  client_id: b21929f60cb26fe36e48926c33f1903c
  client_id: 60dbeb998a0310cfca301870b5f7dab9

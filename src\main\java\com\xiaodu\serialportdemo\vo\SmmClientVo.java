package com.xiaodu.serialportdemo.vo;

/**
 * ClassName: SmmClient
 * Package: com.serial.port.vo
 * Description:
 *
 * @Create 2025-01-17 14:05
 */
public class SmmClientVo {
    //    串口号
    private String com;
    //    波特率
    private String baudRate;
    //    数据位（5、6、7、8）
    private String dataBit;
    //    校验位（N、E、O、M、S）
    private String verifyBit;
    //    停止位（1、1.5、2）
    private String stopBit;

    public String getCom() {
        return com;
    }

    public void setCom(String com) {
        this.com = com;
    }

    public String getBaudRate() {
        return baudRate;
    }

    public void setBaudRate(String baudRate) {
        this.baudRate = baudRate;
    }

    public String getDataBit() {
        return dataBit;
    }

    public void setDataBit(String dataBit) {
        this.dataBit = dataBit;
    }

    public String getVerifyBit() {
        return verifyBit;
    }

    public void setVerifyBit(String verifyBit) {
        this.verifyBit = verifyBit;
    }

    public String getStopBit() {
        return stopBit;
    }

    public void setStopBit(String stopBit) {
        this.stopBit = stopBit;
    }
}

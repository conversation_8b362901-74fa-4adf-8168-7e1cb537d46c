{"version": 3, "file": "js/app.c811722b.js", "mappings": "mEAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,gBAAgB,EACrH,EACIG,EAAkB,GCQtB,GACAC,KAAA,OCXuQ,I,SCQnQC,GAAY,OACd,EACAR,EACAM,GACA,EACA,KACA,KACA,MAIF,EAAeE,EAAiB,Q,UCnB5BR,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,eAAe,CAACM,YAAY,CAAC,SAAW,WAAW,CAACN,EAAG,YAAY,CAACM,YAAY,CAAC,cAAc,iCAAiC,MAAQ,OAAO,YAAY,OAAO,cAAc,OAAO,aAAa,WAAW,CAACR,EAAIS,GAAG,gBAAgBP,EAAG,eAAe,CAACA,EAAG,WAAW,CAACM,YAAY,CAAC,SAAW,SAAS,OAAS,QAAQJ,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,UAAU,CAACQ,YAAY,aAAa,CAACR,EAAG,MAAM,CAACQ,YAAY,WAAWN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,aAAa,CAACQ,YAAY,OAAON,MAAM,CAAC,OAAS,OAAO,QAAU,OAAO,UAAY,eAAe,CAACF,EAAG,YAAY,CAACM,YAAY,CAAC,eAAe,QAAQJ,MAAM,CAAC,KAAO,UAAU,KAAO,wBAAwB,OAAS,GAAG,KAAO,QAAQQ,GAAG,CAAC,MAAQZ,EAAIa,OAAO,GAAGX,EAAG,aAAa,CAACQ,YAAY,OAAON,MAAM,CAAC,OAAS,OAAO,QAAU,OAAO,UAAY,eAAe,CAACF,EAAG,YAAY,CAACM,YAAY,CAAC,eAAe,QAAQJ,MAAM,CAAC,KAAO,UAAU,KAAO,kBAAkB,OAAS,GAAG,KAAO,QAAQQ,GAAG,CAAC,MAAQZ,EAAIc,OAAO,GAAGZ,EAAG,OAAO,CAACM,YAAY,CAAC,aAAa,OAAO,cAAc,MAAM,YAAY,SAAS,CAACR,EAAIS,GAAG,WAAW,GAAGP,EAAG,MAAM,CAACM,YAAY,CAAC,MAAQ,QAAQ,OAAS,OAAO,OAAS,SAAS,gBAAgB,OAAO,aAAa,OAAO,SAAW,WAAW,mBAAmB,gCAAgC,CAACN,EAAG,QAAQ,CAACa,IAAI,eAAeX,MAAM,CAAC,MAAQ,OAAO,OAAS,OAAO,GAAK,aAAaF,EAAG,MAAM,CAACM,YAAY,CAAC,SAAW,WAAW,OAAS,IAAI,KAAO,IAAI,UAAU,QAAQ,CAAsB,GAApBR,EAAIgB,aAAuBd,EAAG,YAAY,CAACM,YAAY,CAAC,cAAc,OAAO,aAAa,QAAQJ,MAAM,CAAC,KAAO,qBAAqB,KAAO,OAAO,KAAO,OAAO,OAAS,IAAIQ,GAAG,CAAC,MAAQZ,EAAIiB,aAAajB,EAAIkB,KAA0B,GAApBlB,EAAIgB,aAAsBd,EAAG,YAAY,CAACM,YAAY,CAAC,cAAc,OAAO,aAAa,OAAO,SAAW,WAAW,UAAU,SAASJ,MAAM,CAAC,KAAO,sBAAsB,KAAO,OAAO,OAAS,GAAG,KAAO,WAAWQ,GAAG,CAAC,MAAQZ,EAAImB,WAAWnB,EAAIkB,KAAKhB,EAAG,YAAY,CAACM,YAAY,CAAC,KAAO,IAAI,SAAW,WAAW,UAAU,UAAUJ,MAAM,CAAC,KAAO,sBAAsB,KAAO,OAAO,OAAS,IAAIQ,GAAG,CAAC,MAAQZ,EAAIoB,aAAa,KAAKlB,EAAG,MAAM,CAACM,YAAY,CAAC,MAAQ,QAAQ,OAAS,OAAO,OAAS,SAAS,gBAAgB,OAAO,aAAa,OAAO,SAAW,WAAW,mBAAmB,iCAAiC,CAACN,EAAG,QAAQ,CAACa,IAAI,gBAAgBX,MAAM,CAAC,MAAQ,OAAO,OAAS,OAAO,GAAK,aAAaF,EAAG,MAAM,CAACM,YAAY,CAAC,SAAW,WAAW,OAAS,IAAI,KAAO,IAAI,UAAU,QAAQ,CAAuB,GAArBR,EAAIqB,cAAwBnB,EAAG,YAAY,CAACM,YAAY,CAAC,cAAc,OAAO,aAAa,QAAQJ,MAAM,CAAC,KAAO,qBAAqB,KAAO,OAAO,KAAO,OAAO,OAAS,IAAIQ,GAAG,CAAC,MAAQZ,EAAIsB,cAActB,EAAIkB,KAA2B,GAArBlB,EAAIqB,cAAuBnB,EAAG,YAAY,CAACM,YAAY,CAAC,cAAc,OAAO,aAAa,OAAO,SAAW,WAAW,UAAU,SAASJ,MAAM,CAAC,KAAO,sBAAsB,KAAO,OAAO,OAAS,GAAG,KAAO,WAAWQ,GAAG,CAAC,MAAQZ,EAAIuB,YAAYvB,EAAIkB,KAAKhB,EAAG,YAAY,CAACM,YAAY,CAAC,KAAO,IAAI,SAAW,WAAW,UAAU,UAAUJ,MAAM,CAAC,KAAO,sBAAsB,KAAO,OAAO,OAAS,IAAIQ,GAAG,CAAC,MAAQZ,EAAIwB,cAAc,QAAQ,GAAGtB,EAAG,eAAe,CAACA,EAAG,UAAU,CAACM,YAAY,CAAC,SAAW,WAAW,CAACN,EAAG,UAAU,CAACQ,YAAY,WAAWF,YAAY,CAAC,OAAS,SAAS,CAACN,EAAG,MAAM,CAACQ,YAAY,WAAWN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,OAAO,CAACM,YAAY,CAAC,aAAa,OAAO,cAAc,MAAM,YAAY,SAAS,CAACR,EAAIS,GAAG,SAASP,EAAG,OAAO,CAACuB,MAAuB,GAAjBzB,EAAI0B,UACz8G,oBACA,sBAAsB,CAAC1B,EAAIS,GAAGT,EAAI2B,GAAoB,GAAjB3B,EAAI0B,UAAqB,KAAO,WAAWxB,EAAG,YAAY,CAACM,YAAY,CAAC,MAAQ,SAASJ,MAAM,CAAC,KAAO,UAAU,KAAO,SAAS,MAAQ,IAAIQ,GAAG,CAAC,MAAQZ,EAAI4B,cAAc,CAAC5B,EAAIS,GAAG,WAAW,GAAGP,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,OAAO,OAAS,IAAI,CAACF,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,WAAW,CAACJ,EAAIS,GAAGT,EAAI2B,GAAG3B,EAAI6B,WAAWC,OAAOC,iBAAiB,GAAG7B,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,SAAS,CAACE,MAAM,CAAC,KAAO,WAAW,CAACJ,EAAIS,GAAGT,EAAI2B,GAAG3B,EAAI6B,WAAWC,OAAOE,iBAAiB,GAAG9B,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,MAAM,CAACM,YAAY,CAAC,aAAa,WAAW,CAACN,EAAG,MAAM,CAACuB,MAAMzB,EAAIiC,iBAAiBC,QAAU,EACttB,gBACA,kBAAkB,CAAChC,EAAG,MAAM,CAACM,YAAY,CAAC,QAAU,MAAM,OAAS,iBAAiB,gBAAgB,MAAM,OAAS,oBAAoB,CAACN,EAAG,QAAQ,CAACa,IAAI,YAAYL,YAAY,WAAWyB,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIqC,kBAAmB,CAAI,EAAE,OAASrC,EAAIsC,gBAAgBpC,EAAG,QAAQ,CAACQ,YAAY,WAAWyB,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIqC,kBAAmB,CAAI,EAAE,OAASrC,EAAIuC,YAAYrC,EAAG,OAAO,CAACQ,YAAY,QAAQR,EAAG,QAAQ,CAACQ,YAAY,WAAWyB,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIqC,kBAAmB,CAAI,EAAE,OAASrC,EAAIwC,YAAYtC,EAAG,QAAQ,CAACQ,YAAY,WAAWyB,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIqC,kBAAmB,CAAI,EAAE,OAASrC,EAAIyC,YAAYvC,EAAG,QAAQ,CAACQ,YAAY,WAAWyB,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIqC,kBAAmB,CAAI,EAAE,OAASrC,EAAI0C,YAAYxC,EAAG,QAAQ,CAACQ,YAAY,WAAWyB,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIqC,kBAAmB,CAAI,EAAE,OAASrC,EAAI2C,YAAYzC,EAAG,QAAQ,CAACQ,YAAY,WAAWyB,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIqC,kBAAmB,CAAI,EAAE,OAASrC,EAAI4C,YAAa,IAAM5C,EAAIiC,iBAAiBC,OAAS,EAAGhC,EAAG,QAAQ,CAACQ,YAAY,WAAWe,MAAM,IAAMzB,EAAIiC,iBAAiBC,OAAS,EACn5C,iBACA,WAAWC,SAAS,CAAC,MAAQnC,EAAIiC,iBAAiB,IAAIrB,GAAG,CAAC,OAASZ,EAAI6C,YAAY7C,EAAIkB,KAAM,IAAMlB,EAAIiC,iBAAiBC,OAAS,EAAGhC,EAAG,MAAM,CAACM,YAAY,CAAC,OAAS,OAAO,MAAQ,QAAQJ,MAAM,CAAC,IAAM0C,EAAQ,MAAuB,IAAM,SAAS9C,EAAIkB,SAAkC,GAAxBlB,EAAIqC,iBAA0BnC,EAAG,KAAK,CAACM,YAAY,CAAC,mBAAmB,MAAM,QAAU,OAAO,MAAQ,QAAQ,OAAS,OAAO,cAAc,OAAO,WAAa,UAAU,SAAW,WAAW,KAAO,QAAQ,UAAU,MAAMR,EAAI+C,GAAI/C,EAAIgD,eAAe,SAASC,GAAQ,OAAO/C,EAAG,KAAK,CAACgD,IAAID,EAAOzC,YAAY,CAAC,MAAQ,QAAQ,kBAAkB,OAAO,OAAS,OAAO,cAAc,OAAO,MAAQ,OAAO,cAAc,OAAO,aAAa,UAAUI,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAOpC,EAAImD,YAAYF,EAAO,IAAI,CAACjD,EAAIS,GAAG,IAAIT,EAAI2B,GAAGsB,GAAQ,MAAM,IAAG,GAAGjD,EAAIkB,KAA8B,GAAxBlB,EAAIqC,iBAA0BnC,EAAG,MAAM,CAACM,YAAY,CAAC,SAAW,WAAW,IAAM,QAAQ,KAAO,QAAQ,MAAQ,OAAO,UAAU,QAAQ,CAAER,EAAIoD,wBAA0B,EAAGlD,EAAG,MAAM,CAACQ,YAAY,YAAY,CAACV,EAAI+C,GAAI/C,EAAIqD,SAAS,SAASC,GAAM,OAAOpD,EAAG,OAAO,CAACgD,IAAII,EAAK5C,YAAY,QAAQE,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAOpC,EAAIuD,OAAOD,EAAK,IAAI,CAACtD,EAAIS,GAAG,IAAIT,EAAI2B,GAAG2B,GAAM,MAAM,IAAGpD,EAAG,OAAO,CAACQ,YAAY,QAAQE,GAAG,CAAC,MAAQZ,EAAIwD,WAAW,CAACxD,EAAIS,GAAG,QAAQ,GAAGT,EAAIkB,KAAMlB,EAAIoD,wBAA0B,EAAGlD,EAAG,MAAM,CAACQ,YAAY,YAAY,CAACV,EAAI+C,GAAI/C,EAAIyD,eAAe,SAASH,GAAM,OAAOpD,EAAG,OAAO,CAACgD,IAAII,EAAK5C,YAAY,QAAQE,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAOpC,EAAIuD,OAAOD,EAAK,IAAI,CAACtD,EAAIS,GAAG,IAAIT,EAAI2B,GAAG2B,GAAM,MAAM,IAAGpD,EAAG,MAAM,CAACM,YAAY,CAAC,QAAU,OAAO,cAAc,SAAS,aAAa,WAAW,CAACN,EAAG,OAAO,CAACQ,YAAY,QAAQF,YAAY,CAAC,aAAa,UAAUI,GAAG,CAAC,MAAQZ,EAAIwD,WAAW,CAACxD,EAAIS,GAAG,OAAOP,EAAG,OAAO,CAACQ,YAAY,QAAQF,YAAY,CAAC,cAAc,MAAM,MAAQ,OAAO,mBAAmB,UAAU,MAAQ,QAAQI,GAAG,CAAC,MAAQZ,EAAI0D,UAAU,CAAC1D,EAAIS,GAAG,QAAQP,EAAG,OAAO,CAACQ,YAAY,QAAQF,YAAY,CAAC,cAAc,MAAM,MAAQ,OAAO,mBAAmB,MAAM,MAAQ,QAAQI,GAAG,CAAC,MAAQZ,EAAI2D,QAAQ,CAAC3D,EAAIS,GAAG,WAAW,GAAGT,EAAIkB,OAAOlB,EAAIkB,SAAShB,EAAG,uBAAuB,CAACM,YAAY,CAAC,QAAU,OAAO,cAAc,UAAUJ,MAAM,CAAC,MAAQ,UAAU,CAACF,EAAG,SAAS,CAAC0D,MAAO,CAAC5D,EAAI6D,YAAazD,MAAM,CAAC,KAAO,WAAW,CAACJ,EAAIS,GAAGT,EAAI2B,GAAG3B,EAAI8D,QAAQ,OAAO5D,EAAG,OAAO,CAACM,YAAY,CAAC,YAAY,OAAO,MAAQ,MAAM,cAAc,OAAO,QAAU,UAAU,CAACR,EAAIS,GAAGT,EAAI2B,GAAG3B,EAAI+D,aAAa,GAAG7D,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,OAAO,CAACF,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,SAASQ,GAAG,CAAC,MAAQZ,EAAIgE,kBAAkBC,MAAM,CAACC,MAAOlE,EAAI0B,UAAWyC,SAAS,SAAUC,GAAMpE,EAAI0B,UAAU0C,CAAG,EAAEC,WAAW,cAAc,CAACnE,EAAG,kBAAkB,CAACE,MAAM,CAAC,OAAQ,IAAO,CAACJ,EAAIS,GAAG,QAAQP,EAAG,kBAAkB,CAACM,YAAY,CAAC,cAAc,QAAQJ,MAAM,CAAC,OAAQ,IAAQ,CAACJ,EAAIS,GAAG,SAAS,IAAI,GAAGP,EAAG,uBAAuB,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,MAAM,CAACM,YAAY,CAAC,MAAQ,QAAQ,OAAS,OAAO,OAAS,WAAW,CAAGR,EAAIsE,QAA8GpE,EAAG,WAAW,CAACM,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQJ,MAAM,CAAC,IAAMJ,EAAIsE,QAAQ,mBAAmB,CAACtE,EAAIsE,YAAlNpE,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM0C,EAAQ,MAA2B,MAAQ,OAAO,OAAS,OAAO,IAAM,OAAgI,GAAG5C,EAAG,MAAM,CAACM,YAAY,CAAC,MAAQ,QAAQ,OAAS,OAAO,mBAAmB,MAAM,OAAS,SAAS,cAAc,SAAS,CAAGR,EAAIuE,SAA+GrE,EAAG,WAAW,CAACM,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQJ,MAAM,CAAC,IAAMJ,EAAIuE,SAAS,mBAAmB,CAACvE,EAAIuE,aAAnNrE,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM0C,EAAQ,MAA2B,MAAQ,OAAO,OAAS,OAAO,IAAM,OAAkI,MAAM,IAAI,IAAI,GAAG5C,EAAG,YAAY,CAACM,YAAY,CAAC,mBAAmB,UAAU,QAAU,aAAa,CAACN,EAAG,MAAM,CAACQ,YAAY,cAAc,CAACR,EAAG,YAAY,CAACE,MAAM,CAAC,KAA0B,GAAnBJ,EAAIwE,YAAuB,UAAY,UAAU5D,GAAG,CAAC,MAAQZ,EAAIyE,eAAe,CAACzE,EAAIS,GAAGT,EAAI2B,GAAG3B,EAAI0E,eAAexE,EAAG,YAAY,CAACU,GAAG,CAAC,MAAQZ,EAAI2E,YAAY,CAAC3E,EAAIS,GAAG,UAAUP,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,SAAWJ,EAAI4E,eAAehE,GAAG,CAAC,MAAQZ,EAAI6E,aAAa,CAAC7E,EAAIS,GAAG,SAAS,MAAM,IAAI,IAAI,GAAGP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAI8E,mBAAmB,MAAQ,UAAUlE,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAI8E,mBAAmB1C,CAAM,IAAI,CAAClC,EAAG,UAAU,CAACQ,YAAY,mBAAmBN,MAAM,CAAC,QAAS,EAAK,MAAQJ,EAAI+E,YAAYC,SAAS,CAAC,OAAS,SAAS5C,GAAQA,EAAO6C,gBAAiB,IAAI,CAAC/E,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,QAAQ,CAACF,EAAG,WAAW,CAACE,MAAM,CAAC,YAAc,SAAS,KAAO,SAASQ,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAOpC,EAAIkF,WAAW,EAAE,GAAGF,SAAS,CAAC,MAAQ,SAAS5C,GAAQ,OAAIA,EAAO+C,KAAKC,QAAQ,QAAQpF,EAAIqF,GAAGjD,EAAOkD,QAAQ,QAAQ,GAAGlD,EAAOc,IAAI,SAAgB,KAAYlD,EAAIkF,WAAW,EAAE,GAAGjB,MAAM,CAACC,MAAOlE,EAAI+E,WAAWQ,WAAYpB,SAAS,SAAUC,GAAMpE,EAAIwF,KAAKxF,EAAI+E,WAAY,aAAcX,EAAI,EAAEC,WAAW,4BAA4B,GAAGnE,EAAG,eAAe,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,QAAQ,eAAe,sBAAsB,KAAO,gBAAgB,iBAAiBJ,EAAIyF,cAAc,kBAAkB,IAAI,oBAAoB,OAAO,kBAAkB,OAAO,MAAQ,QAAQ,eAAe,CAAC,WAAY,aAAa7E,GAAG,CAAC,OAASZ,EAAI0F,YAAYzB,MAAM,CAACC,MAAOlE,EAAI2F,YAAaxB,SAAS,SAAUC,GAAMpE,EAAI2F,YAAYvB,CAAG,EAAEC,WAAW,kBAAkB,IAAI,GAAIrE,EAAI4F,WAAY1F,EAAG,WAAW,CAAC2F,WAAW,CAAC,CAACvF,KAAK,UAAUwF,QAAQ,YAAY5B,MAAOlE,EAAI+F,QAAS1B,WAAW,YAAYjE,MAAM,CAAC,KAAOJ,EAAIgG,SAAS,OAAS,OAAO,uBAAuB,kBAAkB,CAAC9F,EAAG,kBAAkB,CAACE,MAAM,CAAC,SAAW,WAAW,MAAQ,MAAM,MAAQ,OAAO6F,YAAYjG,EAAIkG,GAAG,CAAC,CAAChD,IAAI,UAAUiD,GAAG,SAASC,GAAO,MAAO,CAAClG,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAACR,EAAIS,GAAGT,EAAI2B,GAAGyE,EAAMC,IAAIC,cAAc,IAAI,MAAK,EAAM,cAAcpG,EAAG,kBAAkB,CAACE,MAAM,CAAC,SAAW,aAAa,MAAQ,OAAO,YAAY,MAAM,MAAQ,UAAU6F,YAAYjG,EAAIkG,GAAG,CAAC,CAAChD,IAAI,UAAUiD,GAAG,SAASC,GAAO,MAAO,CAAClG,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAACR,EAAIS,GAAGT,EAAI2B,GAAGyE,EAAMC,IAAIE,eAAe,IAAI,MAAK,EAAM,cAAcrG,EAAG,kBAAkB,CAACE,MAAM,CAAC,SAAW,SAAS,MAAQ,UAAU6F,YAAYjG,EAAIkG,GAAG,CAAC,CAAChD,IAAI,UAAUiD,GAAG,SAASC,GAAO,MAAO,CAAEA,EAAMC,IAAIvC,OAAS,GAAI5D,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAACR,EAAIS,GAAGT,EAAI2B,GAAGyE,EAAMC,IAAIvC,OAAO0C,QAAQ,OAAQJ,EAAMC,IAAIvC,QAAU,GAAI5D,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAGT,EAAI2B,GAAGyE,EAAMC,IAAIvC,OAAO0C,QAAQ,OAAOtG,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,UAAU,cAAc,QAAQ,CAACR,EAAIS,GAAGT,EAAI2B,GAAGyE,EAAMC,IAAIvC,WAAW,IAAI,MAAK,EAAM,cAAc5D,EAAG,kBAAkB,CAACE,MAAM,CAAC,SAAW,WAAW,MAAQ,MAAM6F,YAAYjG,EAAIkG,GAAG,CAAC,CAAChD,IAAI,UAAUiD,GAAG,SAASC,GAAO,MAAO,CAAwB,MAAtBA,EAAMC,IAAII,SAAkBvG,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,OAAO,KAAO,YAAY,CAACJ,EAAIS,GAAG,IAAIT,EAAI2B,GAAG,MAAM,OAAO3B,EAAIkB,KAA4B,OAAtBkF,EAAMC,IAAII,SAAmBvG,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,OAAO,KAAO,WAAW,CAACJ,EAAIS,GAAG,IAAIT,EAAI2B,GAAG,MAAM,OAAO3B,EAAIkB,KAAK,IAAI,MAAK,EAAM,cAAchB,EAAG,kBAAkB,CAACE,MAAM,CAAC,SAAW,SAAS,MAAQ,QAAQ6F,YAAYjG,EAAIkG,GAAG,CAAC,CAAChD,IAAI,UAAUiD,GAAG,SAASC,GAAO,MAAO,CAAsB,KAApBA,EAAMC,IAAIK,OAAexG,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,OAAO,KAAO,WAAW,CAACJ,EAAIS,GAAG,IAAIT,EAAI2B,GAAG,OAAO,OAAO3B,EAAIkB,KAA0B,KAApBkF,EAAMC,IAAIK,OAAexG,EAAG,SAAS,CAACE,MAAM,CAAC,OAAS,OAAO,KAAO,YAAY,CAACJ,EAAIS,GAAG,IAAIT,EAAI2B,GAAG,OAAO,OAAO3B,EAAIkB,KAAK,IAAI,MAAK,EAAM,WAAWhB,EAAG,kBAAkB,CAACE,MAAM,CAAC,MAAQ,KAAK,MAAQ,UAAU6F,YAAYjG,EAAIkG,GAAG,CAAC,CAAChD,IAAI,UAAUiD,GAAG,SAASC,GAAO,MAAO,CAAClG,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,UAAU,SAAWJ,EAAI2G,cAAc/F,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAOpC,EAAI4G,WAAWR,EAAMS,OAAQT,EAAMC,IAAI,IAAI,CAACrG,EAAIS,GAAG,YAAY,IAAI,MAAK,EAAM,eAAe,GAAGT,EAAIkB,KAAMlB,EAAI8G,SAASC,MAAQ,EAAG7G,EAAG,gBAAgB,CAACE,MAAM,CAAC,eAAeJ,EAAI8G,SAASE,WAAW,aAAa,CAAC,GAAI,GAAI,GAAI,KAAK,YAAY,GAAG,OAAS,0CAA0C,MAAQhH,EAAI8G,SAASC,OAAOnG,GAAG,CAAC,cAAcZ,EAAIiH,iBAAiB,iBAAiBjH,EAAIkH,uBAAuBlH,EAAIkB,MAAM,GAAGhB,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,OAAO,QAAUJ,EAAImH,iBAAiB,MAAQ,MAAM,eAAenH,EAAIoH,YAAY,aAAapH,EAAIqH,WAAWzG,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAImH,iBAAiB/E,CAAM,IAAI,CAAClC,EAAG,OAAO,CAACF,EAAIS,GAAG,SAASP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAGT,EAAI2B,GAAG3B,EAAIsH,YAAYtH,EAAIS,GAAG,oBAAoBP,EAAG,OAAO,CAACQ,YAAY,gBAAgBN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,YAAY,CAACU,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAOpC,EAAIuH,QAAQ,KAAK,IAAI,CAACvH,EAAIS,GAAG,QAAQP,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWQ,GAAG,CAAC,MAAQ,SAASwB,GAAQ,OAAOpC,EAAIuH,QAAQ,KAAK,IAAI,CAACvH,EAAIS,GAAG,SAAS,KAAKP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,SAAS,QAAUJ,EAAIwH,mBAAmB,aAAaxH,EAAIqH,UAAU,MAAQ,OAAOzG,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAIwH,mBAAmBpF,CAAM,IAAI,CAAClC,EAAG,OAAO,CAACF,EAAIS,GAAG,SAASP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAGT,EAAI2B,GAAG3B,EAAIsH,YAAYtH,EAAIS,GAAG,WAAWP,EAAG,OAAO,CAACQ,YAAY,gBAAgBN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWQ,GAAG,CAAC,MAAQZ,EAAIyH,aAAa,CAACzH,EAAIS,GAAG,SAAS,KAAKP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,KAAK,QAAUJ,EAAI0H,iBAAiB,MAAQ,MAAM,eAAe1H,EAAI2H,gBAAgB/G,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAI0H,iBAAiBtF,CAAM,IAAI,CAAClC,EAAG,OAAO,CAACF,EAAIS,GAAG,wBAAwBP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAG,UAAUT,EAAIS,GAAG,mBAAmBP,EAAG,OAAO,CAACQ,YAAY,gBAAgBN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWQ,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAI0H,kBAAmB,CAAK,IAAI,CAAC1H,EAAIS,GAAG,UAAU,KAAKP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,KAAK,QAAUJ,EAAI4H,iBAAiB,MAAQ,MAAM,eAAe5H,EAAI6H,gBAAgBjH,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAI4H,iBAAiBxF,CAAM,IAAI,CAAClC,EAAG,OAAO,CAACF,EAAIS,GAAG,wBAAwBP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAG,UAAUT,EAAIS,GAAG,MAAMP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAG,QAAQT,EAAIS,GAAG,mBAAmBP,EAAG,OAAO,CAACQ,YAAY,gBAAgBN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWQ,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAI4H,kBAAmB,CAAK,IAAI,CAAC5H,EAAIS,GAAG,UAAU,KAAKP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,KAAK,QAAUJ,EAAI8H,mBAAmB,MAAQ,MAAM,eAAe9H,EAAI2H,gBAAgB/G,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAI8H,mBAAmB1F,CAAM,IAAI,CAAClC,EAAG,OAAO,CAACF,EAAIS,GAAG,wBAAwBP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAG,UAAUT,EAAIS,GAAG,mBAAmBP,EAAG,OAAO,CAACQ,YAAY,gBAAgBN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWQ,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAI8H,oBAAqB,CAAK,IAAI,CAAC9H,EAAIS,GAAG,UAAU,KAAKP,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,KAAK,QAAUJ,EAAI+H,kBAAkB,MAAQ,MAAM,eAAe/H,EAAI6H,gBAAgBjH,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAI+H,kBAAkB3F,CAAM,IAAI,CAAClC,EAAG,OAAO,CAACF,EAAIS,GAAG,wBAAwBP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAG,UAAUT,EAAIS,GAAG,MAAMP,EAAG,OAAO,CAACM,YAAY,CAAC,MAAQ,MAAM,cAAc,QAAQ,CAACR,EAAIS,GAAG,QAAQT,EAAIS,GAAG,mBAAmBP,EAAG,OAAO,CAACQ,YAAY,gBAAgBN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,YAAY,CAACE,MAAM,CAAC,KAAO,WAAWQ,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAI+H,mBAAoB,CAAK,IAAI,CAAC/H,EAAIS,GAAG,UAAU,KAAMT,EAAIgI,oBAAqB9H,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,WAAW,QAAUJ,EAAIgI,oBAAoB,MAAQ,MAAM,IAAM,MAAM,eAAe,gBAAgBpH,GAAG,CAAC,iBAAiB,SAASwB,GAAQpC,EAAIgI,oBAAoB5F,CAAM,IAAI,CAAClC,EAAG,MAAM,CAACQ,YAAY,mBAAmB,CAACR,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACR,EAAG,WAAW,CAACM,YAAY,CAAC,MAAQ,OAAO,OAAS,SAASJ,MAAM,CAAC,IAAMJ,EAAIiI,SAAS,mBAAmBjI,EAAIkI,UAAU,CAAChI,EAAG,MAAM,CAACQ,YAAY,cAAc,CAACV,EAAIS,GAAG,OAAOP,EAAG,OAAO,CAACQ,YAAY,QAAQ,CAACV,EAAIS,GAAG,aAAaP,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACV,EAAIS,GAAG,aAAa,GAAGP,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACR,EAAG,WAAW,CAACM,YAAY,CAAC,MAAQ,OAAO,OAAS,SAASJ,MAAM,CAAC,IAAMJ,EAAImI,SAAS,mBAAmBnI,EAAIkI,UAAU,CAAChI,EAAG,MAAM,CAACQ,YAAY,cAAc,CAACV,EAAIS,GAAG,OAAOP,EAAG,OAAO,CAACQ,YAAY,QAAQ,CAACV,EAAIS,GAAG,aAAaP,EAAG,MAAM,CAACQ,YAAY,iBAAiB,CAACV,EAAIS,GAAG,aAAa,KAAKP,EAAG,OAAO,CAACQ,YAAY,gBAAgBN,MAAM,CAAC,KAAO,UAAUO,KAAK,UAAU,CAACT,EAAG,YAAY,CAACU,GAAG,CAAC,MAAQ,SAASwB,GAAQpC,EAAIgI,qBAAsB,CAAK,IAAI,CAAChI,EAAIS,GAAG,SAAS,GAAGP,EAAG,MAAM,CAACQ,YAAY,0BAA0BV,EAAIkB,MAAM,EAC14Y,EACIb,EAAkB,G,+KC6yBtB,MAAA6C,EAAAkF,IAAAA,IAAAC,KAAAC,MAAA,oBAEA,OACAhI,KAAA,MACAiI,WAAA,GACAC,IAAAA,GACA,MAAAC,EAAA,IAAAC,KAEAC,EAAA,IAAAD,KAAAD,EAAAG,SAAA,UAEAC,EAAA,IAAAH,KAAAD,EAAAG,SAAA,eAIAE,EAAAC,IAAAJ,GAAAK,OAAA,uBACAC,EAAAF,IAAAF,GAAAG,OAAA,uBACA,OACAE,KAAA,GACAC,KAAA,GACAC,UAAA,GACAvF,WAAA,CACAwF,SAAA,QAEAC,mBAAA,EACAC,SAAA,GACA5C,cAAA,EACAsB,SAAA,KACAE,SAAA,KACAD,QAAA,GACAF,qBAAA,EAEAnG,WAAA,CACAC,OAAA,CACAC,YAAA,OACAC,YAAA,SAGA+D,SAAA,EACAJ,YAAA,CAAAmD,EAAAG,GAEAxD,cAAA,CACA+D,UAAA,CACA,CACAC,KAAA,OACAC,OAAAA,CAAAC,GACA,MAAAC,EAAA,IAAAlB,KACAmB,EAAA,IAAAnB,KACAmB,EAAAC,QAAAD,EAAAE,UAAA,QACAJ,EAAAK,MAAA,QAAAH,EAAAD,GACA,GAEA,CACAH,KAAA,QACAC,OAAAA,CAAAC,GACA,MAAAC,EAAA,IAAAlB,KACAmB,EAAA,IAAAnB,KACAmB,EAAAC,QAAAD,EAAAE,UAAA,QACAJ,EAAAK,MAAA,QAAAH,EAAAD,GACA,GAEA,CACAH,KAAA,QACAC,OAAAA,CAAAC,GACA,MAAAC,EAAA,IAAAlB,KACAmB,EAAA,IAAAnB,KACAmB,EAAAC,QAAAD,EAAAE,UAAA,QACAJ,EAAAK,MAAA,QAAAH,EAAAD,GACA,KAIAK,QAAA,KACAC,QAAA,KACApI,OAAA,KACAgF,SAAA,CACAE,WAAA,EACAmD,SAAA,GACApD,MAAA,GAEAM,WAAA,EACA+C,UAAA,GACAC,WAAA,GACAvG,OAAA,EACAwD,QAAA,GACAgD,OAAA,EACAC,aAAA,GACAC,OAAA,KACAC,KAAA,KACAC,OAAA,KACAC,aAAA,GACAC,aAAA,GACAC,QAAA,KACA1D,kBAAA,EACAK,oBAAA,EACAsD,eAAA,EACAC,eAAA,EACAC,WAAA,GACAC,YAAA,GACArG,eAAA,EACAsG,QAAA,KACAC,QAAA,KACApF,SAAA,EACAqF,OAAA,GACAC,eAAA,GACA3J,WAAA,EACAkE,YAAA,EAEA5E,cAAA,EACAsD,QAAA,GACAC,SAAA,GACAG,WAAA,OACAF,aAAA,EACAnD,eAAA,EACAiK,UAAA,KACAC,WAAA,KACAC,YAAA,oBACAC,YAAA,oBACAC,YAAA,oBAEAC,QAAA,wBACAC,OAAA,YAEAC,aAAA,GACA7F,SAAA,GACAjB,WAAA,CACAQ,WAAA,GACAoD,UAAAG,EACAD,QAAAI,GAEA6C,cAAA,CACA,UACA,UACA,UACA,UACA,UACA,WAEAhH,oBAAA,EACAzC,kBAAA,EACA0J,YAAA,EACA9J,iBAAA,GACAoB,QAAA,CAEA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAEAI,cAAA,CACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KAEAuI,SAAA,GAEAC,SAAA,GACA7I,uBAAA,EACA8I,YAAA,EACAxE,kBAAA,EACAE,kBAAA,EACAE,oBAAA,EACAC,mBAAA,EACAoE,YAAA,EACAC,MAAA,KACAC,UAAA,EACAC,gBAAA,GACAC,YAAA,GAEA,EACAC,OAAAA,GAEAC,OAAAC,iBAAA,eACA,KAAAC,oBACA,KAAAC,qBACA,KAAAC,qBACAC,QAAAC,KAAA,qBAIA,KAAAC,YAQA,KAAAC,SACAC,UAMAC,aAAA,KAEA,IAAAC,EAAA1E,KAAA2E,MAGA,KAAAvC,cAAAsC,EAAA,KAAApC,WAAA,IACA,KAAAD,cAAAqC,EAAA,KAAAnC,YAAA,MACA,KAEA,KAAAqC,eACA,EACAC,aAAAA,GAEA,KAAAjC,YACA,KAAAA,UAAAkC,QACA,KAAAlC,UAAAmC,SACA,KAAAnC,UAAAoC,qBACA,KAAApC,UAAAqC,WAEA,KAAApC,aACA,KAAAA,WAAAiC,QACA,KAAAjC,WAAAkC,SACA,KAAAlC,WAAAmC,qBACA,KAAAnC,WAAAoC,UAEA,EACAC,MAAA,CAYAxC,MAAAA,CAAAyC,EAAAC,GACA,GAAAD,GAAA,QAAAnM,UAAA,CACA,IAAAqM,EAAA,CACAC,SAAA,KAAAnM,WAAAC,OAAAmM,GACA7D,UAAA,KAAAvI,WAAAC,OAAAsI,UACA9D,UAAA,KAAA8E,QAGA8C,EAAAA,EACAC,KAAA,KAAAxC,QAAA,mBAAAoC,GACAK,MAAAL,IACAjB,QAAAuB,IAAAN,EAAAvF,KAAAA,KAAA,eACA,OAAAuF,EAAAvF,KAAAA,MACA,KAAA8F,SAAAC,QAAAR,EAAAvF,KAAAA,KAAA,IAEAgG,OAAAC,IACA3B,QAAAuB,IAAAI,EAAA,eACA,KAAAH,SAAAI,MAAAD,EAAA,IAGA3B,QAAAuB,IAAA,oBAAAR,EAGA,CACA,EACAnM,SAAAA,CAAAmM,EAAAC,GACA,GAAAD,EAAA,OACA,IAAAE,EAAA,CACAC,SAAA,KAAAnM,WAAAC,OAAAmM,GACA7D,UAAA,KAAAvI,WAAAC,OAAAsI,UACA9D,UAAA,KAAA8E,QAEA,KAAAA,QACA8C,EAAAA,EACAC,KAAA,KAAAxC,QAAA,mBAAAoC,GACAK,MAAAL,IACAjB,QAAAuB,IAAAN,EAAAvF,KAAAA,KAAA,eACA,OAAAuF,EAAAvF,KAAAA,MACA,KAAA8F,SAAAC,QAAAR,EAAAvF,KAAAA,KAAA,IAEAgG,OAAAC,IACA3B,QAAAuB,IAAAI,EAAA,eACA,KAAAH,SAAAI,MAAAD,EAAA,GAGA,EAEApM,gBAAAA,CAAAwL,EAAAC,GACA,KAAAR,cAAA,IACAO,IACA,KAAArJ,aAAA,EACA,KAAAC,eACAqI,QAAAuB,IAAA,YAEA,KAAAvM,OAAA6M,YACA,eAAAnD,YAAA,yBACA,CAAAoD,IAAA,IACA,CAAAH,EAAAF,KACAE,GACA3B,QAAAuB,IAAA,iBAEA,IAIA,KAAAnE,QAAAyE,YACA,eAAAlD,YAAA,yBACA,CAAAmD,IAAA,IACA,CAAAH,EAAAF,KAIA,IAIA,KAAAtE,QAAA0E,YACA,eAAAjD,YAAA,yBACA,CAAAkD,IAAA,IACA,CAAAH,EAAAF,KAIA,IAKA,GAEAM,SAAA,CACA7L,aAAAA,GACA,YAAA8I,cAAAgD,MAAA,IACA,GAEAC,QAAA,CACA,gBAAAnI,CAAAqH,EAAA5H,GACA,IACA,KAAAM,cAAA,EACA,MAAAoH,QAAAG,EAAAA,EAAAc,IAAA,QAAArD,2BAAA,CACAsD,OAAA,CAAAhB,GAAA5H,EAAA4H,OAGA,UAAAiB,EAAA,UAAAC,GAAApB,EAAAvF,KAAAA,KACA,KAAAP,SAAAiH,EACA,KAAA/G,SAAAgH,EACA,KAAAjH,QAAA,CAAAgH,EAAAC,GACA,KAAAnH,qBAAA,CACA,OAAA0G,GACA5B,QAAA4B,MAAA,UAAAA,GACA,KAAAJ,SAAAI,MAAA,SACA,SACA,KAAA/H,cAAA,CACA,CACA,EA0BAjB,UAAAA,CAAA0J,GACA,SAAAA,EAIA,OAHA,KAAArK,WAAA4D,UAAA,KACA,KAAA5D,WAAA8D,QAAA,KAEA,KAAA3D,aAEA4H,QAAAuB,IAAA,KAAA1I,YAAA,QACA,KAAAZ,WAAA4D,UAAA,KAAAhD,YAAA,GACA,KAAAZ,WAAA8D,QAAA,KAAAlD,YAAA,GACA,KAAAmB,SAAAE,WAAA,EACA,KAAA9B,YACA,EACAlB,gBAAAA,CAAAE,GAEA4I,QAAAuB,IAAA,KAAA3M,WACAoL,QAAAuB,IAAA,WAIA,EACAgB,aAAAA,GACAC,SAAAC,gBAAAC,oBACAF,SAAAC,gBAAAE,0BACAH,SAAAC,gBAAAG,uBACAJ,SAAAC,gBAAAI,qBACA,EAEA3C,SAAAA,GACAF,QAAAuB,IAAA,KAAA1C,QAAA,kBACAuC,EAAAA,EACAc,IAAA,KAAArD,QAAA,qBACAyC,MAAAL,IACAjB,QAAAuB,IAAAN,EAAA,kBACA,KAAAlM,WAAAkM,EAAAvF,KACA,KAAAgD,YAAA,KAAA3J,WAAA+N,QAAA,GAAAC,GACA,KAAApE,YAAA,KAAA5J,WAAA+N,QAAA,GAAAC,GAEA,KAAAhO,WAAA+N,QAAA1N,OAAA,IACA,KAAAwJ,YAAA,KAAA7J,WAAA+N,QAAA,GAAAC,IAIA,KAAAC,WACA,KAAAC,WAAA,IAEAvB,OAAAE,IAEA5B,QAAA4B,MAAA,QAAAA,EAAA,GAEA,EAMAjK,YAAAA,GACA,KAAAG,eAAA,EACA,KAAAJ,aAAA,KAAAA,YACA,KAAAA,aACA,KAAAE,WAAA,OACA,KAAA1D,cAAA,EACA,KAAAsK,UAAA0E,mBAAA,KAAA9E,SACA,KAAAI,UAAAmC,SAEA,KAAAnC,UAAA1K,GAAAqP,IAAAA,OAAAC,OAAA,CAAAzB,EAAA0B,KAEA1B,GAAAwB,IAAAA,WAAAG,aACAtD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAI,0BACAvD,QAAAuB,IAAA,YAEAI,GAAAwB,IAAAA,WAAAK,eACAxD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAM,6BACAzD,QAAAuB,IAAA,cAEAI,GAAAwB,IAAAA,WAAAO,aACA1D,QAAAuB,IAAA,QAAA8B,EACA,IAEA,KAAA7E,UAAAmF,OACA,KAAAnF,UAAAoF,OACA,KAAA/D,oBACA,KAAAC,qBAEA,KAAA/K,WAAA+N,QAAA1N,OAAA,GACA,KAAA2K,qBAGA,KAAAxL,eAAA,EACA,KAAAkK,WAAAyE,mBAAA,KAAA7E,SACA,KAAAI,WAAAkC,SAEA,KAAAlC,WAAA3K,GAAAqP,IAAAA,OAAAC,OAAA,CAAAzB,EAAA0B,KAEA1B,GAAAwB,IAAAA,WAAAG,aACAtD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAI,0BACAvD,QAAAuB,IAAA,YAEAI,GAAAwB,IAAAA,WAAAK,eACAxD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAM,6BACAzD,QAAAuB,IAAA,cAEAI,GAAAwB,IAAAA,WAAAO,aACA1D,QAAAuB,IAAA,QAAA8B,EACA,IAEA,KAAA5E,WAAAkF,OACA,KAAAlF,WAAAmF,SAGA,KAAAhM,WAAA,OACA,KAAAiM,uBACA,KAAAC,wBAEA,KAAA/O,WAAA+N,QAAA1N,OAAA,GACA,KAAA2O,wBAGA,EACAlE,iBAAAA,GACA,KAAA7K,QACA,KAAAA,OAAA8H,MAKA,KAAA9H,OAAAgP,EAAAA,EAAAC,QAAA,CACAC,SAAA,KACAC,SAAA,KAAArF,OAEAsF,KAAA,QACAzG,KAAA,KACA0G,SAAA,QACAC,SAAA,WACAC,kBAAA,GACAC,YAAA,EACAtD,SAAA,cAAAuD,SAAA,IAAAC,KAAAC,SAAA,MAGA,KAAA3P,OAAAlB,GAAA,gBACAkM,QAAAuB,IAAA,wCAEA,KAAAvM,OAAA4P,UACA,eAAAlG,YAAA,kCACA,CAAAoD,IAAA,IACA,CAAAH,EAAAkD,KACA7E,QAAAuB,IAAA,4BAAAsD,GACAlD,GACA3B,QAAAuB,IAAA,sBAAAsD,EACA,IAQA,KAAA7P,OAAA4P,UACA,eAAAlG,YAAA,yBACA,CAAAoD,IAAA,IACA,CAAAH,EAAAkD,KAEAlD,GACA3B,QAAAuB,IAAA,sBAAAsD,EACA,IAIA,KAAA7P,OAAA4P,UACA,eAAAlG,YAAA,+BACA,CAAAoD,IAAA,IACA,CAAAH,EAAAkD,KAEAlD,GACA3B,QAAAuB,IAAA,sBAAAsD,EACA,IAIA,KAAA7P,OAAA4P,UACA,eAAAlG,YAAA,yBACA,CAAAoD,IAAA,IACA,CAAAH,EAAAF,KACAE,GACA3B,QAAAuB,IAAA,yBACA,GAEA,IAEA,KAAAvM,OAAAlB,GAAA,YAAAgR,EAAAC,KACA,GAAAD,GAAA,eAAApG,YAAA,0BACA,MAAAX,EAAAiH,KAAAxJ,MAAAuJ,EAAAE,YAQA,GAPAjF,QAAAuB,IAAAxD,EAAA,YACA,KAAAvG,QACA,yBACAuG,EAAAA,QAAAmH,eAAA/O,OAAAgP,YACAC,mBACA,KAAAC,YACAtH,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAE,YACA,KAAAzQ,UAIA,GAHAoL,QAAAuB,IAAA,2BACAvB,QAAAuB,IAAAxD,EAAA,cACA,KAAAA,QAAAiH,KAAAxJ,MAAAuJ,EAAAE,YAGA,GADAlH,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAG,cAGA,KAAA9K,QAAA,KAAA+K,aACAxH,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAK,SAEA,KAAAnL,kBAAA,MACA,CACA,IAAAoL,EACA1H,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAK,QACA,eAAAC,EAGA,OAFA,KAAAnH,OAAA,GACA,KAAAnJ,iBAAA,GACA,KAAAqM,SAAA,CACAuD,QAAA,SACA1M,KAAA,YAGA,IAAAqN,EAAA,KAAAH,aAAAE,GACA,KAAAE,WAAAD,EAAA,QACA1F,QAAAuB,IAAA,KAAAjD,OAAA,QAEA,CAIA0B,QAAAuB,IAAAxD,EAAA,WACA,CACA,GACA+G,GACA,eAAApG,YAAA,kCACA,CACA,MAAAkH,EAAAZ,KAAAxJ,MAAAuJ,EAAAE,YACAjF,QAAAuB,IAAAqE,EAAA,UACA,CAEA,GACAd,GACA,eAAApG,YAAA,+BACA,CACAsG,KAAAxJ,MAAAuJ,EAAAE,WAEA,CACA,GAAAH,GAAA,eAAApG,YAAA,0BACA,MAAAkH,EAAAZ,KAAAxJ,MAAAuJ,EAAAE,YAEA,KAAA/G,WAAA,IAAA0H,EAAAtF,SAIA,CAEAN,QAAAuB,IACA,6BAAAuD,MAAAC,EAAAE,aACA,IAEA,KAAAjQ,OAAAlB,GAAA,SAAA6N,IACA3B,QAAA4B,MAAA,cAAAD,EAAA,GAEA,EACA7B,kBAAAA,GACA,KAAA1C,SACA,KAAAA,QAAAN,MAIA,KAAAM,QAAA4G,EAAAA,EAAAC,QAAA,CACAC,SAAA,KACAC,SAAA,KAAArF,OAEAsF,KAAA,QACAzG,KAAA,KACA0G,SAAA,QACAC,SAAA,WACAC,kBAAA,GACAC,YAAA,EACAtD,SAAA,eAAAuD,SAAA,IAAAC,KAAAC,SAAA,MAEA,KAAAvH,QAAAtJ,GAAA,gBACAkM,QAAAuB,IAAA,wCAEA,KAAAnE,QAAAwH,UACA,eAAAjG,YAAA,yBACA,CAAAmD,IAAA,IACA,CAAAH,EAAAkD,KACA7E,QAAAuB,IAAA,UAAAsD,GACAlD,GACA3B,QAAAuB,IAAA,sBAAAsD,EACA,IAIA,KAAAzH,QAAAwH,UACA,eAAAjG,YAAA,yBACA,CAAAmD,IAAA,IACA,CAAAH,EAAAF,KACAzB,QAAAuB,IAAAE,EAAA,WACAE,GACA3B,QAAAuB,IAAA,0BACA,GAEA,IAEA,KAAAnE,QAAAtJ,GAAA,YAAAgR,EAAAC,KACA,GAAAD,GAAA,eAAAnG,YAAA,0BACA,MAAAZ,EAAAiH,KAAAxJ,MAAAuJ,EAAAE,YAKA,GAJA,KAAAxN,SACA,yBACAsG,EAAAA,QAAAmH,eAAA/O,OAAAgP,YACAC,oBACA,KAAAxQ,UAKA,GAJAoL,QAAAuB,IAAA,yCACAvB,QAAAuB,IAAAxD,EAAA,cACA,KAAAA,QAAAiH,KAAAxJ,MAAAuJ,EAAAE,YAIA,GADAlH,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAG,cAGA,KAAA9K,QAAA,KAAA+K,aACAxH,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAK,SAEA,KAAAnL,kBAAA,MACA,CACA,IAAAoL,EACA1H,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAK,QACA,eAAAC,EAGA,OAFA,KAAAnH,OAAA,GACA,KAAAnJ,iBAAA,GACA,KAAAqM,SAAA,CACAuD,QAAA,SACA1M,KAAA,YAGA,IAAAqN,EAAA,KAAAH,aAAAE,GAEA,KAAAE,WAAAD,EAAA,QACA1F,QAAAuB,IAAA,KAAAjD,OAAA,QAEA,CAGA0B,QAAAuB,IAAAxD,EAAA,YAEAiC,QAAAuB,IACAxD,EAAAA,QAAAmH,eAAA/O,OAAAgP,YACAC,mBACA,UAGA,KAAAC,YACAtH,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAE,WACA,CACA,GACAP,GACA,eAAAnG,YAAA,kCACA,CACA,MAAAiH,EAAAZ,KAAAxJ,MAAAuJ,EAAAE,YACAjF,QAAAuB,IAAAqE,EAAA,UACA,CACA,GAAAd,GAAA,eAAAnG,YAAA,0BACA,MAAAiH,EAAAZ,KAAAxJ,MAAAuJ,EAAAE,YACAjF,QAAAuB,IAAAqE,EAAA,SACA,KAAAzH,YAAA,IAAAyH,EAAAtF,SAIA,CAEAN,QAAAuB,IACA,6BAAAuD,MAAAC,EAAAE,aACA,IAEA,KAAA7H,QAAAtJ,GAAA,SAAA6N,IACA3B,QAAA4B,MAAA,cAAAD,EAAA,GAGA,EAQAkE,iBAAAA,CAAAnK,GAGA,IAAAtE,EAAA,KAAAqI,YAAAqG,SAAApK,GACAsE,QAAAuB,IAAAnK,EAAA,eAEAA,GAWA4I,QAAAuB,IAAA,OAAA7F,EAAA,cACA,KAAA8F,SAAA,CACAnJ,KAAA,UACA0M,QAAA,OAAArJ,EAAA,iBAbA,KAAA+D,YAAAsG,KAAArK,GACA,KAAAsK,cAAAtK,GAEAuK,YAAA,KACA,IAAAC,EAAAxK,EACA,KAAA+D,YAAA,KAAAA,YAAA0G,QAAA3P,GACAA,IAAA0P,GACA,GACA,KAQA,EAOAnG,kBAAAA,GACA,KAAA5C,SACA,KAAAA,QAAAL,MAKA,KAAAK,QAAA6G,EAAAA,EAAAC,QAAA,CACAC,SAAA,KACAC,SAAA,KAAArF,OAEAsF,KAAA,QACAzG,KAAA,KACA0G,SAAA,QACAC,SAAA,WACAC,kBAAA,GACAC,YAAA,EAEAtD,SAAA,eAAAuD,SAAA,IAAAC,KAAAC,SAAA,MAEA,KAAAxH,QAAArJ,GAAA,gBACAkM,QAAAuB,IAAA,wCAEA,KAAApE,QAAAyH,UACA,eAAAhG,YAAA,yBACA,CAAAkD,IAAA,IACA,CAAAH,EAAAkD,KACA7E,QAAAuB,IAAA,UAAAsD,GACAlD,GACA3B,QAAAuB,IAAA,sBAAAsD,EACA,IAIA,KAAA1H,QAAAyH,UACA,eAAAhG,YAAA,yBACA,CAAAkD,IAAA,IACA,CAAAH,EAAAF,KACAzB,QAAAuB,IAAAE,EAAA,WACAE,GACA3B,QAAAuB,IAAA,0BACA,IAKA,KAAApE,QAAAyH,UACA,eAAAhG,YAAA,+BACA,CAAAkD,IAAA,IACA,CAAAH,EAAAkD,KACA7E,QAAAuB,IAAA,SAAAsD,GAEAlD,GACA3B,QAAAuB,IAAA,sBAAAsD,EACA,GAEA,IAEA,KAAA1H,QAAArJ,GAAA,YAAAgR,EAAAC,KACA,GAAAD,GAAA,eAAAlG,YAAA,0BACA,MAAAb,EAAAiH,KAAAxJ,MAAAuJ,EAAAE,YACAjF,QAAAuB,IAAAxD,EAAA,kBACA,IAAA0H,EACA1H,EAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAK,QAEA,GADAxF,QAAAuB,IAAAkE,EAAA,2BACA,YAAAA,EACA,YAAAjE,SAAA,CACAuD,QAAA,6BACA1M,KAAA,YAGA,IAAAqN,EAAA,KAAAH,aAAAE,GACAzF,QAAAuB,IAAAyD,KAAAoB,UAAA,CAAA5M,UAAAkM,IAAA,SAEA,IAAAhK,EAAA,KAAA2K,QAAArB,KAAAoB,UAAA,CAAA5M,UAAAkM,KACA1F,QAAAuB,IAAA,KAAA9D,aAAA,cAEA2D,EAAAA,EACAc,IAAA,KAAArD,QAAA,qBAAAnD,EAAA,CACA4K,QAAA,CACA,iDACAC,MAAA,KAAA9I,gBAGA6D,MAAAL,IACAjB,QAAAuB,IAAAN,EAAA,gBACA,IAAAuF,EAAAvF,EAAAvF,KACA+K,EAAAD,EAAAE,MAAAlQ,GAAAA,IAAAkP,IAEA1F,QAAAuB,IAAAkF,EAAA,MACAA,EACA,KAAAZ,kBAAAH,IAEA,KAAAlE,SAAA,CACAnJ,KAAA,QACA0M,QAAA,MAAAW,EAAA,kBAEA,KAAApH,OAAA,GACA,KAAAnJ,iBAAA,GACA,IAEAuM,OAAAC,IACA3B,QAAAuB,IAAAI,EAAA,WAEA3B,QAAAuB,IAAAmE,EAAA,mBAGA1F,QAAAuB,IAAAxD,EAAA,WACA,CACA,GACA+G,GACA,eAAAlG,YAAA,kCACA,CACA,MAAAgH,EAAAZ,KAAAxJ,MAAAuJ,EAAAE,YACAjF,QAAAuB,IAAAqE,EAAA,UACA,CACA,GAAAd,GAAA,eAAAlG,YAAA,0BACA,MAAAgH,EAAAZ,KAAAxJ,MAAAuJ,EAAAE,YACAjF,QAAAuB,IAAAqE,EAAA,SACA,KAAAzH,YAAA,IAAAyH,EAAAtF,SACA,CAGA,GACAwE,GACA,eAAAlG,YAAA,+BACA,CACA,MAAA+H,EAAA3B,KAAAxJ,MAAAuJ,EAAAE,YASA,GARAjF,QAAAuB,IAAAoF,EAAA,aACAA,EAAAxF,KACA,KAAA9E,KAAAsK,EAAAxF,GAAAa,MAAA,MACA,KAAA5F,KAAA,eACA4D,QAAAuB,IAAA,KAAAlF,KAAA,WACA2D,QAAAuB,IAAA,KAAAnF,KAAA,cAGA,KAAAuK,EAAAC,MAAA,KAAAxK,MAAA,KAAAC,KAAA,CACA,KAAAmF,SAAA,CACAnJ,KAAA,UACA0M,QAAA,gBAEA,IAAA8B,EAAA,CACA3F,SAAA,KAAAnM,WAAAC,OAAAmM,GACA9I,KAAA,KACAmB,UAAA,KAAAgG,gBACAsH,SAAA,GAGA1F,EAAAA,EACAC,KAAA,KAAAxC,QAAA,yBAAAgI,GACAvF,MAAAL,IACAjB,QAAAuB,IAAAN,EAAA,WAEAS,OAAAC,IACA3B,QAAAuB,IAAAI,EAAA,UAEA,MACA3B,QAAAuB,IAAAoF,EAAA,aACA,KAAAvK,MAAA,KAAAC,MACA,KAAAmF,SAAA,CACAnJ,KAAA,QACA0M,QAAA,SAKA,CAEA/E,QAAAuB,IACA,6BAAAuD,MAAAC,EAAAE,aACA,IAEA,KAAA9H,QAAArJ,GAAA,SAAA6N,IACA3B,QAAA4B,MAAA,cAAAD,EAAA,GAEA,EAQA/K,OAAAA,GAEA,GADAoJ,QAAAuB,IAAA,KAAA/D,MAAA,MACA,KAAArI,iBAAAC,QAAA,EAIA,GAHA4K,QAAAuB,IAAA,YAAApM,iBAAA4R,KAAA,KACA,KAAAzI,OAAA,KAAAnJ,iBAAA4R,KAAA,IACA/G,QAAAuB,MACA,KAAA3M,UAIA,QAAAoC,OAAA,EACA,KAAA2O,WAAA,KAAArH,OAAA,UACA,CACA,IAAA5C,EAAA,KAAA2K,QAAArB,KAAAoB,UAAA,CAAA5M,UAAA,KAAA8E,UACA8C,EAAAA,EACAc,IAAA,KAAArD,QAAA,qBAAAnD,EAAA,CACA4K,QAAA,CACA,iDACAC,MAAA,KAAA9I,gBAGA6D,MAAAL,IACAjB,QAAAuB,IAAAN,EAAA,gBACA,IAAAuF,EAAAvF,EAAAvF,KACA+K,EAAAD,EAAAE,MAAAlQ,GAAAA,IAAA,KAAA8H,SAEA0B,QAAAuB,IAAAkF,EAAA,MACAA,GAIA,KAAA7R,WACA,KAAAiR,kBAAA,KAAAvH,QAEA,KAAA/I,kBAAA,GAEA,KAAAiM,SAAA,CACAnJ,KAAA,QACA0M,QAAA,oBAEA,IAEArD,OAAAC,IACA3B,QAAAuB,IAAAI,EAAA,UAEA,MAtCA,KAAAgE,WAAA,KAAArH,OAAA,KAwCA,EAEAvK,EAAAA,GACA4L,OAAAqH,SAAAC,QACA,EAMAjT,EAAAA,GACA,KAAAmJ,SACA,KAAA4G,wBAEA,KAAAhE,qBACAC,QAAAuB,IAAA,YAEA,IAAA2F,EAAA,CACA/F,GAAA,UACA4B,GAAA,KAAAnE,YACApL,KAAA,cACA2T,QAAA,MACA7G,UAAA,WACAvC,QAAA,CACA1F,KAAA,cACA+O,KAAA,KAGApH,QAAAuB,IAAA,QAEA,KAAApE,QAAAkK,QACA,eAAAzI,YAAA,4BACAoG,KAAAoB,UAAAc,GAEA,EAGAlB,aAAAA,CAAAxG,GACA,KAAAA,gBAAAA,EACA,KAAAO,qBAEA,MAAAoB,EAAA,eAAAsD,SAAA,IAAAC,KAAAC,SAAA,IACA,KAAArI,UAAAyJ,KAAA5E,GACAnB,QAAAuB,IAAA,QACA,IAAAY,EAAA,CACAhB,GAAAA,EACA4B,GAAA,KAAAnE,YACApL,KAAA,WACA2T,QAAA,MACA7G,UAAA,WACAvC,QAAA,CACA1F,KAAA,WACA+O,KAAA,CACAE,MAAA,IACAC,GAAA,EACAnQ,MAAA,KAIA,KAAA+F,QAAAkK,QACA,eAAAzI,YAAA,yBACAoG,KAAAoB,UAAAjE,GAEA,EAEAqF,aAAAA,GACA,IAAArF,EAAA,CACA3I,UAAA,KAAAvB,WAAAQ,WACAoD,UAAA,KAAA5D,WAAA4D,UACAE,QAAA,KAAA9D,WAAA8D,QACA7B,WAAA,EACAmD,SAAA,IAGA+D,EAAAA,EACAc,IAAA,KAAArD,QAAA,2BAAAsD,WACAb,MAAAmG,IACAzH,QAAAuB,IAAAkG,EAAA/L,KAAA,QACAsE,QAAAuB,IAAAY,EAAA,UACA,KAAAjJ,SAAAuO,EAAA/L,KAAAA,KAEA,KAAA1B,SAAAC,MAAAwN,EAAA/L,KAAAzB,MACA,KAAAD,SAAAE,WAAA,KAEAwH,OAAAE,IAEA5B,QAAA4B,MAAAA,EAAA,GAEA,EACAxJ,UAAAA,CAAAsP,GACA,KAAAzO,SAAA,EACA,GAAAyO,IACA,KAAA1N,SAAAE,WAAA,GAGA,IAAAiI,EAAA,CACA3I,UAAA,KAAAvB,WAAAQ,WACAoD,UAAA,KAAA5D,WAAA4D,UACAE,QAAA,KAAA9D,WAAA8D,QACA7B,WAAA,KAAAF,SAAAE,WACAmD,SAAA,KAAArD,SAAAqD,UAEA2C,QAAAuB,IAAAY,EAAA,UACAf,EAAAA,EACAc,IAAA,KAAArD,QAAA,2BAAAsD,WAEAb,MAAAmG,IACAzH,QAAAuB,IAAAkG,EAAA/L,KAAA,QACA,KAAAxC,SAAAuO,EAAA/L,KAAAA,KACA,KAAA1B,SAAAC,MAAAwN,EAAA/L,KAAAzB,MACA,KAAAhB,SAAA,KAEAyI,OAAAE,IAEA5B,QAAA4B,MAAAA,EAAA,GAEA,EACAzH,gBAAAA,CAAAmI,GACA,KAAAtI,SAAAqD,SAAAiF,EACA,KAAAtI,SAAAE,WAAA,EACA,KAAA9B,aACA4H,QAAAuB,IAAA,MAAAe,MACA,EACAlI,mBAAAA,CAAAkI,GACA,KAAAtI,SAAAE,WAAAoI,EACA,KAAAlK,aACA4H,QAAAuB,IAAA,QAAAe,IACA,EAGAqF,cAAAA,GACA,IAAAjM,EAAA,GACA0F,EAAAA,EACAC,KAAAuG,CAAAA,SAAAA,aAAAA,SAAAA,KAAAC,iBAAA,wBAAAnM,EAAA,CACA4K,QAAA,CACA,mDACAC,MAAA,KAAA9I,gBAGA6D,MAAAL,IACAjB,QAAAuB,IAAAN,EAAA,UACA,KAAA1D,WAAA0D,EAAAvF,KAAAoM,IAAA,IAEApG,OAAAE,IAEA5B,QAAA4B,MAAA,QAAAA,EAAA,GAEA,EAGA+D,UAAAA,CAAArH,EAAAyJ,GACA/H,QAAAuB,IAAAyD,KAAAoB,UAAA,CAAA5M,UAAA8E,IAAA,SAEA,IAAA5C,EAAA,KAAA2K,QAAArB,KAAAoB,UAAA,CAAA5M,UAAA8E,KACA0B,QAAAuB,IAAA,KAAA9D,aAAA,cACA2D,EAAAA,EACAc,IAAA,KAAArD,QAAA,qBAAAnD,EAAA,CACA4K,QAAA,CACA,iDACAC,MAAA,KAAA9I,gBAGA6D,MAAAL,IACAjB,QAAAuB,IAAAN,EAAA,gBACA,IAAAuF,EAAAvF,EAAAvF,KACA+K,EAAAD,EAAAE,MAAAlQ,GAAAA,IAAA8H,IAKA,GAHA0B,QAAAuB,IAAAkF,EAAA,MACA,KAAAjJ,MAAAiJ,EACAzG,QAAAuB,IAAA,KAAA/D,MAAA,gBACAiJ,GAAA,MAAAsB,EACA/H,QAAAuB,IAAA,QACA,KAAAhM,kBAAA,EACA,KAAAuC,eAAA,OACA,GAAA2O,GAAA,MAAAsB,EAAA,CAGA,IAAAC,EAAA,KAAAxQ,QAAAwQ,MACA,0CAEAC,EAAA,KAAAxQ,SAAAuQ,MACA,0CAGA,GAAAA,EACA,IAAAE,EAAAF,EAAA,GAEA,GAAAC,EACA,IAAAE,EAAAF,EAAA,GAKA,KAAAlT,WAAAC,OAAAsI,UAEA,KAAAgB,OAEA,KAAAtH,OAEA,KAAApC,UARA,IAYAwT,EAAA,CACApR,OAAA,KAAAA,OAEAwC,UAAA,KAAA8E,OACA8D,UAAA8F,EACA7F,UAAA8F,EACAxO,SAAA,KAAA/E,UAAA,YAGA,QAAAoC,OAAA,EAMA,OALA,KAAAwK,SAAA,CACAuD,QAAA,aACA1M,KAAA,iBAEA,KAAAP,eAAA,GAIA,GACA,QAAAsH,aACA,KAAApI,OAAA,IACA,QAAApC,UAKA,OAHA,KAAAgG,kBAAA,EACA,KAAAwE,mBACA,KAAAtH,eAAA,GAIA,GACA,QAAAsH,aACA,KAAApI,OAAA,IACA,QAAApC,UAKA,OAHA,KAAAkG,kBAAA,EACA,KAAAsE,mBACA,KAAAtH,eAAA,GAKA,GACA,QAAAsH,aACA,KAAApI,QAAA,IACA,QAAApC,UAKA,OAHA,KAAAoG,oBAAA,EACA,KAAAoE,mBACA,KAAAtH,eAAA,GAIA,GACA,QAAAsH,aACA,KAAApI,QAAA,IACA,QAAApC,UAKA,OAHA,KAAAqG,mBAAA,EACA,KAAAmE,mBACA,KAAAtH,eAAA,GAIAsJ,EAAAA,EACAC,KAAA,KAAAxC,QAAA,kBAAAuJ,EAAA,CACA9B,QAAA,CACA,oDAGAhF,MAAAmG,IACA,KAAAA,EAAA/L,KAAAkL,MACA,KAAA9O,eAAA,EACA,KAAAsH,YAAA,EACA,KAAAoC,SAAA,CACAuD,QAAA,OACA1M,KAAA,YAEA2H,QAAAuB,IAAA,UACAvB,QAAAuB,IAAAkG,EAAA/L,KAAA,MACA,KAAAlE,QAAA,GACA,KAAAC,SAAA,GACA,KAAAT,OAAA,GACA,KAAA7B,iBAAA,GAGA,KAAAH,SACA,KAAA0C,aAAA,EACA,KAAAC,eACA,KAAA3C,OAAA4P,UACA,eAAAlG,YAAA,yBACA,CAAAoD,IAAA,IACA,CAAAH,EAAAF,KACAE,EAGA3B,QAAAuB,IAAA,YAFAvB,QAAAuB,IAAA,YAGA,IAGA,KAAAnE,QAAAwH,UACA,eAAAjG,YAAA,yBACA,CAAAmD,IAAA,IACA,CAAAH,EAAAF,KACAE,EAGA3B,QAAAuB,IAAA,YAFAvB,QAAAuB,IAAA,YAGA,IAGA,KAAApE,QAAAyH,UACA,eAAAhG,YAAA,yBACA,CAAAkD,IAAA,IACA,CAAAH,EAAAF,KACAE,EAGA3B,QAAAuB,IAAA,YAFAvB,QAAAuB,IAAA,YAGA,OAKA,KAAAzJ,eAAA,EACA,KAAAsH,YAAA,EACA,KAAAoC,SAAA,CACAuD,QAAA0C,EAAA/L,KAAAqJ,QACA1M,KAAA,YAEA,IAIAqJ,OAAAE,IACA5B,QAAA4B,MAAA,QAAAA,EAAA,GAEA,cAAApE,OAAA,QAAAuK,EACA,KAAAzJ,OAAAA,MAEA,CACA,WAAAyJ,EASA,OARA,KAAArN,oBAAA,EAMA,KAAA4D,OAAA,QACA,KAAAnJ,iBAAA,IAGA,KAAAqM,SAAA,CACAuD,QAAA,kBACA1M,KAAA,WAGA,CACA2H,QAAAuB,IAAA,KAAA/D,MAAA,YAEAkE,OAAAE,IAEA5B,QAAA4B,MAAA,QAAAA,EAAA,GAEA,EAEApB,aAAAA,CAAApJ,GACA,KAAA+H,SAAA/H,EACA4I,QAAAuB,IAAAnK,EAAA,WACA,IAAA+K,EAAA,CACAkG,UAAAjR,GAEA4I,QAAAuB,IAAAY,EAAA,QACAf,EAAAA,EACAc,IAAA,KAAArD,QAAA,sBAAAsD,WACAb,MAAAmG,IACA,KAAAzI,cAAA,GACAyI,EAAA/L,KAAAtG,OAAA,IACA,KAAA4J,cAAAyI,EAAA/L,KAGAsE,QAAAuB,IAAAkG,EAAA/L,MACA,IAEAgG,OAAAE,IAEA5B,QAAA4B,MAAAA,EAAA,GAEA,EAEA7J,UAAAA,GACA,KAAAD,eAAA,EACAkI,QAAAuB,IAAA,MACAvB,QAAAuB,IAAA,KAAAjD,OAAA,YACA,SAAAA,aAAAgK,GAAA,KAAAhK,QASA,KAAAqH,WAAA,KAAArH,OAAA,MACA0B,QAAAuB,IAAA,KAAA/D,MAAA,cATA,KAAAgE,SAAA,CACAuD,QAAA,SACA1M,KAAA,WASA,EAEAgO,OAAAA,CAAA1J,GACA,MAAA4L,EAAAjN,IAAAA,IAAA+K,QAAA1J,EAAAvG,EAAA,CAEAoS,KAAAlN,IAAAA,KAAAmN,IACAC,QAAApN,IAAAA,IAAAqN,QAEA,OAAAJ,EAAAtD,UACA,EAEAtK,UAAAA,GACA,KAAAD,oBAAA,CACA,EAIAkO,SAAAA,GACA,IAAAlN,EAAAsJ,KAAAoB,UAAA,CACAyC,MAAA,KACAC,OAAA,OAGA1H,EAAAA,EACAC,KAAAuG,CAAAA,SAAAA,aAAAA,SAAAA,KAAAC,iBAAA,0BAAAnM,EAAA,CACA4K,QAAA,CACA,oDAGAhF,MAAAL,IACAjB,QAAAuB,IAAAN,EAAA,cACA,KAAAxD,aAAAwD,EAAAvF,KAAA6K,MAEAvG,QAAAuB,IAAA,KAAA9D,aAAA,cAEAiE,OAAAE,IAEA5B,QAAA4B,MAAA,QAAAA,EAAA,GAEA,EAEAtH,WAAAA,GACA,KAAAD,kBAAA,CACA,EACAQ,cAAAA,GACA,KAAAD,kBAAA,CACA,EACAG,cAAAA,GACA,KAAAD,kBAAA,CACA,EACAiO,gBAAAA,GACA,KAAA/N,oBAAA,CACA,EACAgO,eAAAA,GACA,KAAA/N,mBAAA,CACA,EACAR,OAAAA,CAAArD,GACA,SAAAA,EAAA,CACA,KAAAiD,kBAAA,EACA,IAAAoL,EACA,KAAA1H,QAAAA,QAAAmH,eAAA/O,OAAAgP,YAAAK,QAEA,eAAAC,EACA,YAAAjE,SAAA,CACAuD,QAAA,SACA1M,KAAA,YAIA,IAAAqN,EAAA,KAAAH,aAAAE,GAEA,KAAAE,WAAAD,EAAA,OAIA,MACA,KAAArL,kBAAA,EACA,KAAAiE,OAAA,GACA,KAAAnJ,iBAAA,EAEA,EAEAgL,MAAAA,GACA,KAAAZ,WAEA,KAAAD,QACA2J,cAAA,KAAA3J,OACA,KAAAA,MAAA,MAEA,KAAAA,MAAAe,aAAA,KAEAe,EAAAA,EACAc,IAAA,KAAArD,QAAA,qBACAyH,QAAA,CACA,oDAGAhF,MAAAmG,IAGA,QAAAA,EAAA/L,KAAAkL,KACA,KAAApK,mBAAA,EACA,KAAAzF,WAAAwF,SAAA,OACA,KAAAxF,WAAAmS,MAAA,UAEA,KAAAlS,OAAAyQ,EAAA/L,KAAAA,UAEA,QAAA+L,EAAA/L,KAAAkL,OACA,KAAApK,qBAEA,KAAAA,oBAAA,KAEA,IAAA2M,EAAA1B,EAAA/L,KAAAqJ,QAAAqE,UAAA,KACA,KAAAnS,QAAAwQ,EAAA/L,KAAAqJ,QAAAqE,UAAA,GAEA,KAAArS,WAAAwF,SAAA,OACA,KAAAxF,WAAAmS,MAAA,MACA,KAAAlS,OAAAmS,CACA,CAGA,IA6CAzH,OAAAE,OAGA,GAEA,KAEA,EACA2D,YAAAA,CAAA8D,GAEA,IAAAC,EAAAC,KAAAF,GACAG,EAAAF,EAAAlU,OACAqU,EAAA,IAAAC,WAAAF,GACA,QAAAG,EAAA,EAAAA,EAAAH,EAAAG,IACAF,EAAAE,GAAAL,EAAAM,WAAAD,GAEA,IAAAE,EAAA,IAAAC,YACAC,EAAAF,EAAAG,OAAAP,GAIA,OAHAzJ,QAAAuB,IAAAwI,EAAA,WACA,KAAA5U,iBAAA4U,EAAAE,MAAA,IAEAF,CACA,EACAlG,oBAAAA,GACA,KAAA7O,OAAA8H,KAAA,KACAkD,QAAAuB,IAAA,mCAEA,EACAuC,qBAAAA,GACA,KAAA1G,QAAAN,KAAA,KACAkD,QAAAuB,IAAA,mCAEA,EACAwC,qBAAAA,GACA,KAAA5G,QAAAL,KAAA,KACAkD,QAAAuB,IAAA,mCAEA,EAEA1J,SAAAA,GACA,KAAAC,eAAA,EAEA,IAAAqK,EAAA,CACAhB,GAAA,UACA4B,GAAA,KAAArE,YACAlL,KAAA,cACA2T,QAAA,MACA7G,UAAA,WACAvC,QAAA,CACA1F,KAAA,cACA+O,KAAA,KAIA8C,EAAA,CACA/I,GAAA,SACA4B,GAAA,KAAApE,YACAnL,KAAA,cACA2T,QAAA,MACA7G,UAAA,WACAvC,QAAA,CACA1F,KAAA,cACA+O,KAAA,KAGApH,QAAAuB,IAAA,QAEA,KAAAvM,OAAAqS,QACA,eAAA3I,YAAA,4BACAsG,KAAAoB,UAAAjE,IAEAnC,QAAAuB,IAAA,KAAA5C,YAAAwD,EAAA,cACA,KAAA/E,QAAAiK,QACA,eAAA1I,YAAA,4BACAqG,KAAAoB,UAAA8D,GAEA,EAEAlH,QAAAA,GAEA,IAAAtH,EAAA,CACArD,KAAA,uBACA8R,OAAA,mBAGAnK,QAAAuB,IACA,eAAAxM,WAAA+N,QAAA,GAAAsH,GAAA,eACA,QAGAhJ,EAAAA,EACAC,KAAA,eAAAtM,WAAA+N,QAAA,GAAAsH,GAAA,eAAA1O,GACA4F,MAAAL,IAEA,KAAAsF,MAAAtF,EAAAvF,KAAA0L,KAAAb,MACApD,IAAAA,eACA,KAAA/E,QAAAoE,SAAA6H,eAAA,WAEA,KAAA7L,UAAA2E,IAAAA,aAAA,CACA9K,KAAA,MAEAiS,IAAA,aAAAvV,WAAA+N,QAAA,GAAAsH,wBAAA,KAAA7D,oBAGA,KAAA/H,UAAA0E,mBAAA,KAAA9E,SAEA,KAAAI,UAAA1K,GAAAqP,IAAAA,OAAAC,OAAA,CAAAzB,EAAA0B,KAEA1B,GAAAwB,IAAAA,WAAAG,aACAtD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAI,0BACAvD,QAAAuB,IAAA,YAEAI,GAAAwB,IAAAA,WAAAK,eACAxD,QAAAuB,IAAA,QAGA8B,GAAAF,IAAAA,aAAAM,6BACAzD,QAAAuB,IAAA,cAEAI,GAAAwB,IAAAA,WAAAO,aACA1D,QAAAuB,IAAA,QAAA8B,EACA,IAGA,KAAA7E,UAAAmF,QAKA3D,QAAAuB,IAAA,2CACA,IAEAG,OAAAE,IAEA5B,QAAA4B,MAAA,QAAAA,EAAA,GAEA,EACAqB,SAAAA,GACA,IAAAvH,EAAA,CACArD,KAAA,uBACA8R,OAAA,mBAEA/I,EAAAA,EACAC,KAAA,eAAAtM,WAAA+N,QAAA,GAAAsH,GAAA,eAAA1O,GACA4F,MAAAL,IAEA,KAAAsJ,OAAAtJ,EAAAvF,KAAA0L,KAAAb,MACApD,IAAAA,eACA,KAAA9E,QAAAmE,SAAA6H,eAAA,WAEA,KAAA5L,WAAA0E,IAAAA,aAAA,CACA9K,KAAA,MACAiS,IAAA,aAAAvV,WAAA+N,QAAA,GAAAsH,wBAAA,KAAAG,qBAGA,KAAA9L,WAAAyE,mBAAA,KAAA7E,SAEA,KAAAI,WAAA3K,GAAAqP,IAAAA,OAAAC,OAAA,CAAAzB,EAAA0B,KAEA1B,GAAAwB,IAAAA,WAAAG,aACAtD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAI,0BACAvD,QAAAuB,IAAA,YAEAI,GAAAwB,IAAAA,WAAAK,eACAxD,QAAAuB,IAAA,QAEA8B,GAAAF,IAAAA,aAAAM,6BACAzD,QAAAuB,IAAA,cAEAI,GAAAwB,IAAAA,WAAAO,aACA1D,QAAAuB,IAAA,QAAA8B,EACA,IAGA,KAAA5E,WAAAkF,QAGA3D,QAAAuB,IAAA,2CACA,IAEAG,OAAAE,IAEA5B,QAAA4B,MAAA,QAAAA,EAAA,GAEA,EACAzN,SAAAA,GACA6L,QAAAuB,IAAA,OACA,KAAAvD,eACA,KAAAgF,WAEA,KAAA9O,cAAA,EACA,KAAAsK,UAAA0E,mBAAA,KAAA9E,SACA,KAAAI,UAAAmC,SAEA,KAAAnC,UAAA1K,GAAAqP,IAAAA,OAAAC,OAAA,CAAAzB,EAAA0B,KAEA1B,GAAAwB,IAAAA,WAAAG,aACAtD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAI,0BACAvD,QAAAuB,IAAA,YAEAI,GAAAwB,IAAAA,WAAAK,eACAxD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAM,6BACAzD,QAAAuB,IAAA,cAEAI,GAAAwB,IAAAA,WAAAO,aACA1D,QAAAuB,IAAA,QAAA8B,EACA,IAEA,KAAA7E,UAAAmF,OACA,KAAAnF,UAAAoF,MACA,EACApP,UAAAA,GACA,KAAAD,eAAA,EACA,KAAAkK,WAAAyE,mBAAA,KAAA7E,SACA,KAAAI,WAAAkC,SAEA,KAAAlC,WAAA3K,GAAAqP,IAAAA,OAAAC,OAAA,CAAAzB,EAAA0B,KAEA1B,GAAAwB,IAAAA,WAAAG,aACAtD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAI,0BACAvD,QAAAuB,IAAA,YAEAI,GAAAwB,IAAAA,WAAAK,eACAxD,QAAAuB,IAAA,QACA8B,GAAAF,IAAAA,aAAAM,6BACAzD,QAAAuB,IAAA,cAEAI,GAAAwB,IAAAA,WAAAO,aACA1D,QAAAuB,IAAA,QAAA8B,EACA,IAEA,KAAA5E,WAAAkF,OACA,KAAAlF,WAAAmF,MACA,EACAvP,OAAAA,GACA,KAAAH,cAAA,EACA,KAAAsK,YACA,KAAAA,UAAAkC,QACA,KAAAlC,UAAAmC,SAEA,EACAlM,QAAAA,GACA,KAAAF,eAAA,EACA,KAAAkK,aACA,KAAAA,WAAAiC,QACA,KAAAjC,WAAAkC,SAEA,EACArM,QAAAA,GACA,IAAAkW,EAAAhI,SAAA6H,eAAA,WACAG,EAAA9H,kBACA8H,EAAA9H,oBACA8H,EAAAC,yBACAD,EAAAC,yBAEA,EACA/V,SAAAA,GACA,IAAA8V,EAAAhI,SAAA6H,eAAA,WACAG,EAAA9H,kBACA8H,EAAA9H,oBACA8H,EAAAC,yBACAD,EAAAC,yBAEA,EAEA3V,WAAAA,GACA,KAAAkD,oBAAA,EACA,KAAAc,YAAA,EACA,KAAAkB,SAAAE,WAAA,EACA,KAAAjC,WAAAQ,WAAA,GACA,KAAAL,aACA,KAAAH,WAAAQ,WAAA,EACA,EAEApC,WAAAA,CAAAF,GACA6J,QAAAuB,IAAApL,EAAA,WACA,KAAAhB,iBAAAgB,EAAA8T,MAAA,IACA,KAAA3T,uBAAA,KAAAnB,iBAAAC,MACA,EACAI,YAAAA,CAAAkV,GAGA,KAAAvV,iBAAA,GAAAuV,EAAAC,OAAAvT,MACA,KAAAd,uBAAA,KAAAnB,iBAAAC,OACA,KAAAG,kBAAA,EACAyK,QAAAuB,IACA,KAAApM,iBAAAC,OACA,6BAEA,EACAK,QAAAA,CAAAiV,GACA1K,QAAAuB,IAAAmJ,EAAA,WACAA,EAAAC,OAAAvT,OAAA4I,QAAAuB,IAAA,aACA,KAAApM,iBAAA,GAAAuV,EAAAC,OAAAvT,KACA,EACA1B,QAAAA,CAAAgV,GACA1K,QAAAuB,IAAAmJ,EAAA,WACA,KAAAvV,iBAAA,GAAAuV,EAAAC,OAAAvT,MACA6S,MAAA,IACAW,KAAAC,GACA,WAAAC,KAAAD,GACAA,EAAAE,eACA,QAAAD,KAAAD,GACAA,KAKA9D,KAAA,GACA,EACApR,QAAAA,CAAA+U,GACA1K,QAAAuB,IAAAmJ,EAAA,UACA,KAAAvV,iBAAA,GAAAuV,EAAAC,OAAAvT,KACA,EACAxB,QAAAA,CAAA8U,GACA1K,QAAAuB,IAAAmJ,EAAA,UACA,KAAAvV,iBAAA,GAAAuV,EAAAC,OAAAvT,KACA,EACAvB,QAAAA,CAAA6U,GACA1K,QAAAuB,IAAAmJ,EAAA,UACA,KAAAvV,iBAAA,GAAAuV,EAAAC,OAAAvT,KACA,EACAtB,QAAAA,CAAA4U,GACA1K,QAAAuB,IAAAmJ,EAAA,UACA,KAAAvV,iBAAA,GAAAuV,EAAAC,OAAAvT,KACA,EACArB,QAAAA,CAAA2U,GACA1K,QAAAuB,IAAAmJ,EAAA,UACA,KAAAvV,iBAAA,GAAAuV,EAAAC,OAAAvT,KACA,EACAX,MAAAA,CAAAW,GACA4I,QAAAuB,IAAA,KAAApM,iBAAA4R,KAAA,gBAEA,KAAAxR,kBAAA,EACA,KAAAJ,iBAAAC,QAAA,IACA,KAAAD,iBAAA4Q,KAAA3O,GACA,KAAAd,uBAAA,KAAAnB,iBAAAC,OACA,KAAAoL,cAAA,KAAArL,iBAAA4R,KAAA,KAEA,EACArQ,QAAAA,GACAsJ,QAAAuB,IAAA,KAAApM,iBAAA,gBACA,KAAAA,iBAAA6V,MAGA,KAAAxK,cAAA,KAAArL,iBAAA4R,KAAA,KACA,SAAA5R,iBAAAC,SACA,KAAA0C,eAAA,EACA,KAAAvC,kBAAA,EACA,KAAAe,uBAAA,EACA,KAAAgI,OAAA,GAEA,EACAzH,KAAAA,GACA,KAAA2J,cAAA,IACA,KAAA1I,eAAA,EACA,KAAAvC,kBAAA,EACA,KAAAJ,iBAAA,GACA,KAAAmB,uBAAA,EACA,KAAAgI,OAAA,EACA,IClyFiR,ICQ7Q,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIF,EAAe,EAAiB,QCnB5BrL,EAAS,WAAkB,IAAIC,EAAIC,KAAQD,EAAIG,MAAMD,GAAG,OAAOF,EAAI+X,GAAG,EAC1E,EACI1X,EAAkB,CAAC,WAAY,IAAIL,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,mBAAmB,CAACF,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,YAAYF,EAAG,MAAM,CAACQ,YAAY,gCAAgCR,EAAG,MAAM,CAACQ,YAAY,SAASN,MAAM,CAAC,iBAAiB,GAAG,gBAAgB,MAAMF,EAAG,MAAM,CAACQ,YAAY,cAAc,CAACV,EAAIS,GAAG,wBACpW,GCiBA,GACA+H,IAAAA,GACA,OAEA,CACA,EACAwP,OAAAA,GAIA,EAGAxL,OAAAA,GACA,KAAAyL,UACA,EACAlJ,QAAA,CACAkJ,QAAAA,GACAnL,QAAAuB,IAAA,UACAH,EAAAA,EACAc,IAAA,0CACAZ,MAAAL,IAEAjB,QAAAuB,IAAAN,GACA,GAAAA,EAAAvF,KAAAkL,KACA,KAAAwE,QAAArF,KAAA,SAEA,KAAAoF,UACA,IAGAzJ,OAAAE,IACA,KAAAuJ,WAEAnL,QAAA4B,MAAA,QAAAA,EAAA,GAEA,ICxDgR,ICQ5Q,GAAY,OACd,EACA,EACA,GACA,EACA,KACA,KACA,MAIa,EAAiB,QCdhCyJ,EAAAA,WAAIC,IAAIC,EAAAA,IACR,UAAmBA,EAAAA,GAAO,CACxB/C,KAAM,UACNgD,OAAQ,CACN,CACEpH,KAAM,IAEN3Q,UAAWgY,GAEb,CACErH,KAAM,QAEN3Q,UAAWgY,M,mBCXjBJ,EAAAA,WAAIK,OAAOC,eAAgB,EAC3BN,EAAAA,WAAIC,IAAIM,KACR,IAAIP,EAAAA,WAAI,CACNQ,OAAM,EACN5Y,OAAS6Y,GAAMA,EAAEC,GACjBC,QAAQ,IACPC,OAAO,O,4KCXNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB9D,IAAjB+D,EACH,OAAOA,EAAaC,QAGrB,IAAInC,EAAS+B,EAAyBE,GAAY,CACjDjL,GAAIiL,EACJG,QAAQ,EACRD,QAAS,CAAC,GAUX,OANAE,EAAoBJ,GAAUK,KAAKtC,EAAOmC,QAASnC,EAAQA,EAAOmC,QAASH,GAG3EhC,EAAOoC,QAAS,EAGTpC,EAAOmC,OACf,CAGAH,EAAoBO,EAAIF,E,WC5BxBL,EAAoBQ,KAAO,CAAC,C,eCA5B,IAAIC,EAAW,GACfT,EAAoBU,EAAI,SAAS1W,EAAQ2W,EAAUzT,EAAI0T,GACtD,IAAGD,EAAH,CAMA,IAAIE,EAAeC,IACnB,IAAStD,EAAI,EAAGA,EAAIiD,EAASxX,OAAQuU,IAAK,CACrCmD,EAAWF,EAASjD,GAAG,GACvBtQ,EAAKuT,EAASjD,GAAG,GACjBoD,EAAWH,EAASjD,GAAG,GAE3B,IAJA,IAGIuD,GAAY,EACPC,EAAI,EAAGA,EAAIL,EAAS1X,OAAQ+X,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAaK,OAAOC,KAAKlB,EAAoBU,GAAGS,OAAM,SAASlX,GAAO,OAAO+V,EAAoBU,EAAEzW,GAAK0W,EAASK,GAAK,IAChKL,EAASS,OAAOJ,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbN,EAASW,OAAO5D,IAAK,GACrB,IAAI6D,EAAInU,SACEiP,IAANkF,IAAiBrX,EAASqX,EAC/B,CACD,CACA,OAAOrX,CArBP,CAJC4W,EAAWA,GAAY,EACvB,IAAI,IAAIpD,EAAIiD,EAASxX,OAAQuU,EAAI,GAAKiD,EAASjD,EAAI,GAAG,GAAKoD,EAAUpD,IAAKiD,EAASjD,GAAKiD,EAASjD,EAAI,GACrGiD,EAASjD,GAAK,CAACmD,EAAUzT,EAAI0T,EAwB/B,C,eC5BAZ,EAAoBsB,EAAI,SAAStD,GAChC,IAAIuD,EAASvD,GAAUA,EAAOwD,WAC7B,WAAa,OAAOxD,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAgC,EAAoByB,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNAvB,EAAoByB,EAAI,SAAStB,EAASwB,GACzC,IAAI,IAAI1X,KAAO0X,EACX3B,EAAoB4B,EAAED,EAAY1X,KAAS+V,EAAoB4B,EAAEzB,EAASlW,IAC5EgX,OAAOY,eAAe1B,EAASlW,EAAK,CAAE6X,YAAY,EAAM/L,IAAK4L,EAAW1X,IAG3E,C,eCPA+V,EAAoB+B,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOhb,MAAQ,IAAIib,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,kBAAX1O,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBwM,EAAoB4B,EAAI,SAASO,EAAKC,GAAQ,OAAOnB,OAAOoB,UAAUC,eAAehC,KAAK6B,EAAKC,EAAO,C,eCCtGpC,EAAoBqB,EAAI,SAASlB,GACX,qBAAXoC,QAA0BA,OAAOC,aAC1CvB,OAAOY,eAAe1B,EAASoC,OAAOC,YAAa,CAAEvX,MAAO,WAE7DgW,OAAOY,eAAe1B,EAAS,aAAc,CAAElV,OAAO,GACvD,C,eCNA+U,EAAoByC,IAAM,SAASzE,GAGlC,OAFAA,EAAO0E,MAAQ,GACV1E,EAAO2E,WAAU3E,EAAO2E,SAAW,IACjC3E,CACR,C,eCJAgC,EAAoB4C,EAAI,G,eCKxB,IAAIC,EAAkB,CACrB,IAAK,GAaN7C,EAAoBU,EAAEM,EAAI,SAAS8B,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4BzT,GAC/D,IAKI0Q,EAAU6C,EALVnC,EAAWpR,EAAK,GAChB0T,EAAc1T,EAAK,GACnB2T,EAAU3T,EAAK,GAGIiO,EAAI,EAC3B,GAAGmD,EAASpG,MAAK,SAASvF,GAAM,OAA+B,IAAxB6N,EAAgB7N,EAAW,IAAI,CACrE,IAAIiL,KAAYgD,EACZjD,EAAoB4B,EAAEqB,EAAahD,KACrCD,EAAoBO,EAAEN,GAAYgD,EAAYhD,IAGhD,GAAGiD,EAAS,IAAIlZ,EAASkZ,EAAQlD,EAClC,CAEA,IADGgD,GAA4BA,EAA2BzT,GACrDiO,EAAImD,EAAS1X,OAAQuU,IACzBsF,EAAUnC,EAASnD,GAChBwC,EAAoB4B,EAAEiB,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAO9C,EAAoBU,EAAE1W,EAC9B,EAEImZ,EAAqBC,KAAK,yBAA2BA,KAAK,0BAA4B,GAC1FD,EAAmBE,QAAQN,EAAqBO,KAAK,KAAM,IAC3DH,EAAmBvJ,KAAOmJ,EAAqBO,KAAK,KAAMH,EAAmBvJ,KAAK0J,KAAKH,G,IC/CvF,IAAII,EAAsBvD,EAAoBU,OAAEvE,EAAW,CAAC,MAAM,WAAa,OAAO6D,EAAoB,KAAO,IACjHuD,EAAsBvD,EAAoBU,EAAE6C,E", "sources": ["webpack://quarryweb/./src/App.vue", "webpack://quarryweb/src/App.vue", "webpack://quarryweb/./src/App.vue?60e0", "webpack://quarryweb/./src/App.vue?01fe", "webpack://quarryweb/./src/components/main.vue", "webpack://quarryweb/src/components/main.vue", "webpack://quarryweb/./src/components/main.vue?9257", "webpack://quarryweb/./src/components/main.vue?a81d", "webpack://quarryweb/./src/components/one.vue", "webpack://quarryweb/src/components/one.vue", "webpack://quarryweb/./src/components/one.vue?8cff", "webpack://quarryweb/./src/components/one.vue?13bc", "webpack://quarryweb/./src/utils/router.js", "webpack://quarryweb/./src/main.js", "webpack://quarryweb/webpack/bootstrap", "webpack://quarryweb/webpack/runtime/amd options", "webpack://quarryweb/webpack/runtime/chunk loaded", "webpack://quarryweb/webpack/runtime/compat get default export", "webpack://quarryweb/webpack/runtime/define property getters", "webpack://quarryweb/webpack/runtime/global", "webpack://quarryweb/webpack/runtime/hasOwnProperty shorthand", "webpack://quarryweb/webpack/runtime/make namespace object", "webpack://quarryweb/webpack/runtime/node module decorator", "webpack://quarryweb/webpack/runtime/publicPath", "webpack://quarryweb/webpack/runtime/jsonp chunk loading", "webpack://quarryweb/webpack/startup"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!-- App.vue -->\r\n<template>\r\n  <div id=\"app\">\r\n    <!-- <router-link to=\"/\">Home</router-link> -->\r\n    <!-- <router-link to=\"/about\">About</router-link> -->\r\n    <router-view></router-view>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'App'\r\n};\r\n</script>\r\n<style>\r\nbody {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n#app {\r\n  height: 100vh;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n</style>", "import mod from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=ce1c16bc\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\nimport style0 from \"./App.vue?vue&type=style&index=0&id=ce1c16bc&prod&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('el-container',{staticStyle:{\"overflow\":\"hidden\"}},[_c('el-header',{staticStyle:{\"text-shadow\":\"0 10px 10px rgba(0, 0, 0, 0.2)\",\"color\":\"#fff\",\"font-size\":\"40px\",\"font-weight\":\"bold\",\"font-style\":\"italic\"}},[_vm._v(\"河北省采(弃)砂项目\")]),_c('el-container',[_c('el-aside',{staticStyle:{\"overflow\":\"hidden\",\"height\":\"100%\"},attrs:{\"width\":\"600px\"}},[_c('el-card',{staticClass:\"box-card1\"},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('el-tooltip',{staticClass:\"item\",attrs:{\"effect\":\"dark\",\"content\":\"刷新页面\",\"placement\":\"bottom-end\"}},[_c('el-button',{staticStyle:{\"margin-right\":\"20px\"},attrs:{\"type\":\"success\",\"icon\":\"el-icon-refresh-right\",\"circle\":\"\",\"size\":\"mini\"},on:{\"click\":_vm.SX}})],1),_c('el-tooltip',{staticClass:\"item\",attrs:{\"effect\":\"dark\",\"content\":\"触发抬杆\",\"placement\":\"bottom-end\"}},[_c('el-button',{staticStyle:{\"margin-right\":\"20px\"},attrs:{\"type\":\"warning\",\"icon\":\"el-icon-smoking\",\"circle\":\"\",\"size\":\"mini\"},on:{\"click\":_vm.TG}})],1),_c('span',{staticStyle:{\"text-align\":\"left\",\"font-weight\":\"900\",\"font-size\":\"18px\"}},[_vm._v(\"实时监控\")])],1),_c('div',{staticStyle:{\"width\":\"550px\",\"height\":\"35vh\",\"margin\":\"0 auto\",\"margin-bottom\":\"50px\",\"margin-top\":\"20px\",\"position\":\"relative\",\"background-image\":\"url(./assets/beijingtu.png)\"}},[_c('video',{ref:\"videoElement\",attrs:{\"width\":\"100%\",\"height\":\"100%\",\"id\":\"videoEl\"}}),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"left\":\"0\",\"z-index\":\"999\"}},[(_vm.buttonStatus == false)?_c('el-button',{staticStyle:{\"margin-left\":\"10px\",\"margin-top\":\"15px\"},attrs:{\"icon\":\"el-icon-video-play\",\"type\":\"info\",\"size\":\"mini\",\"circle\":\"\"},on:{\"click\":_vm.flv_start}}):_vm._e(),(_vm.buttonStatus == true)?_c('el-button',{staticStyle:{\"margin-left\":\"10px\",\"margin-top\":\"15px\",\"position\":\"relative\",\"z-index\":\"99999\"},attrs:{\"icon\":\"el-icon-video-pause\",\"size\":\"mini\",\"circle\":\"\",\"type\":\"success\"},on:{\"click\":_vm.flv_end}}):_vm._e(),_c('el-button',{staticStyle:{\"left\":\"0\",\"position\":\"relative\",\"z-index\":\"999999\"},attrs:{\"icon\":\"el-icon-full-screen\",\"size\":\"mini\",\"circle\":\"\"},on:{\"click\":_vm.flv_full}})],1)]),_c('div',{staticStyle:{\"width\":\"550px\",\"height\":\"35vh\",\"margin\":\"0 auto\",\"margin-bottom\":\"70px\",\"margin-top\":\"20px\",\"position\":\"relative\",\"background-image\":\"url(../assets/beijingtu.png)\"}},[_c('video',{ref:\"videoElement2\",attrs:{\"width\":\"100%\",\"height\":\"100%\",\"id\":\"videoE2\"}}),_c('div',{staticStyle:{\"position\":\"absolute\",\"bottom\":\"0\",\"left\":\"0\",\"z-index\":\"999\"}},[(_vm.buttonStatus2 == false)?_c('el-button',{staticStyle:{\"margin-left\":\"10px\",\"margin-top\":\"15px\"},attrs:{\"icon\":\"el-icon-video-play\",\"type\":\"info\",\"size\":\"mini\",\"circle\":\"\"},on:{\"click\":_vm.flv_start2}}):_vm._e(),(_vm.buttonStatus2 == true)?_c('el-button',{staticStyle:{\"margin-left\":\"10px\",\"margin-top\":\"15px\",\"position\":\"relative\",\"z-index\":\"99999\"},attrs:{\"icon\":\"el-icon-video-pause\",\"size\":\"mini\",\"circle\":\"\",\"type\":\"success\"},on:{\"click\":_vm.flv_end2}}):_vm._e(),_c('el-button',{staticStyle:{\"left\":\"0\",\"position\":\"relative\",\"z-index\":\"999999\"},attrs:{\"icon\":\"el-icon-full-screen\",\"size\":\"mini\",\"circle\":\"\"},on:{\"click\":_vm.flv_full2}})],1)])])],1),_c('el-container',[_c('el-main',{staticStyle:{\"overflow\":\"hidden\"}},[_c('el-card',{staticClass:\"box-card\",staticStyle:{\"height\":\"84vh\"}},[_c('div',{staticClass:\"clearfix\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticStyle:{\"text-align\":\"left\",\"font-weight\":\"900\",\"font-size\":\"18px\"}},[_vm._v(\"车辆信息-\"),_c('span',{class:_vm.carStatus == false\n                      ? 'carNoBoxInputTrue'\n                      : 'carNoBoxInputFalse'},[_vm._v(_vm._s(_vm.carStatus == false ? \"出场\" : \"入场\"))])]),_c('el-button',{staticStyle:{\"float\":\"right\"},attrs:{\"type\":\"success\",\"size\":\"small \",\"round\":\"\"},on:{\"click\":_vm.showHistory}},[_vm._v(\"历史数据\")])],1),_c('el-descriptions',{attrs:{\"title\":\"信息展示\",\"column\":1}},[_c('el-descriptions-item',{attrs:{\"label\":\"项目名称\"}},[_c('el-tag',{attrs:{\"size\":\"medium\"}},[_vm._v(_vm._s(_vm.ClientData.client.projectName))])],1),_c('el-descriptions-item',{attrs:{\"label\":\"磅站名称\"}},[_c('el-tag',{attrs:{\"size\":\"medium\"}},[_vm._v(_vm._s(_vm.ClientData.client.stationName))])],1),_c('el-descriptions-item',{attrs:{\"label\":\"车牌号\"}},[_c('div',{staticStyle:{\"text-align\":\"center\"}},[_c('div',{class:_vm.licensePlateUnit.length <= 7\n                        ? 'carNoBoxInput'\n                        : 'carNoBoxInput1'},[_c('div',{staticStyle:{\"padding\":\"6px\",\"border\":\"2px solid #fff\",\"border-radius\":\"6px\",\"margin\":\"6px 3px 6px 6px\"}},[_c('input',{ref:\"inputBox0\",staticClass:\"inputBox\",domProps:{\"value\":_vm.licensePlateUnit[0]},on:{\"click\":function($event){_vm.licensePlateDoor = true},\"change\":_vm.handleSelect}}),_c('input',{staticClass:\"inputBox\",domProps:{\"value\":_vm.licensePlateUnit[1]},on:{\"click\":function($event){_vm.licensePlateDoor = true},\"change\":_vm.license1}}),_c('span',{staticClass:\"dot\"}),_c('input',{staticClass:\"inputBox\",domProps:{\"value\":_vm.licensePlateUnit[2]},on:{\"click\":function($event){_vm.licensePlateDoor = true},\"change\":_vm.license2}}),_c('input',{staticClass:\"inputBox\",domProps:{\"value\":_vm.licensePlateUnit[3]},on:{\"click\":function($event){_vm.licensePlateDoor = true},\"change\":_vm.license3}}),_c('input',{staticClass:\"inputBox\",domProps:{\"value\":_vm.licensePlateUnit[4]},on:{\"click\":function($event){_vm.licensePlateDoor = true},\"change\":_vm.license4}}),_c('input',{staticClass:\"inputBox\",domProps:{\"value\":_vm.licensePlateUnit[5]},on:{\"click\":function($event){_vm.licensePlateDoor = true},\"change\":_vm.license5}}),_c('input',{staticClass:\"inputBox\",domProps:{\"value\":_vm.licensePlateUnit[6]},on:{\"click\":function($event){_vm.licensePlateDoor = true},\"change\":_vm.license6}}),(7 === _vm.licensePlateUnit.length - 1)?_c('input',{staticClass:\"inputBox\",class:7 === _vm.licensePlateUnit.length - 1\n                            ? 'inputBoxActive'\n                            : 'inputBox',domProps:{\"value\":_vm.licensePlateUnit[7]},on:{\"change\":_vm.license7}}):_vm._e(),(7 !== _vm.licensePlateUnit.length - 1)?_c('img',{staticStyle:{\"height\":\"34px\",\"width\":\"34px\"},attrs:{\"src\":require(\"../assets/yezi2.png\"),\"alt\":\"新能源\"}}):_vm._e()])]),(_vm.licensePlateDoor == true)?_c('ul',{staticStyle:{\"background-color\":\"red\",\"display\":\"flex\",\"width\":\"600px\",\"height\":\"36px\",\"line-height\":\"36px\",\"background\":\"#d0d5d9\",\"position\":\"absolute\",\"left\":\"630px\",\"z-index\":\"4\"}},_vm._l((_vm.firstSixItems),function(result){return _c('li',{key:result,staticStyle:{\"width\":\"100px\",\"list-style-type\":\"none\",\"height\":\"36px\",\"line-height\":\"36px\",\"color\":\"#fff\",\"font-weight\":\"bold\",\"text-align\":\"center\"},on:{\"click\":function($event){return _vm.handleClick(result)}}},[_vm._v(\" \"+_vm._s(result)+\" \")])}),0):_vm._e(),(_vm.licensePlateDoor == true)?_c('div',{staticStyle:{\"position\":\"absolute\",\"top\":\"410px\",\"left\":\"630px\",\"width\":\"100%\",\"z-index\":\"999\"}},[(_vm.licensePlateUnitLength <= 0)?_c('div',{staticClass:\"carNoBox\"},[_vm._l((_vm.columns),function(item){return _c('span',{key:item,staticClass:\"carNo\",on:{\"click\":function($event){return _vm.pickOn(item)}}},[_vm._v(\" \"+_vm._s(item)+\" \")])}),_c('span',{staticClass:\"delBt\",on:{\"click\":_vm.delCarNo}},[_vm._v(\"X\")])],2):_vm._e(),(_vm.licensePlateUnitLength >= 1)?_c('div',{staticClass:\"carNoBox\"},[_vm._l((_vm.numberColumns),function(item){return _c('span',{key:item,staticClass:\"carNo\",on:{\"click\":function($event){return _vm.pickOn(item)}}},[_vm._v(\" \"+_vm._s(item)+\" \")])}),_c('div',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\",\"text-align\":\"center\"}},[_c('span',{staticClass:\"delBt\",staticStyle:{\"text-align\":\"center\"},on:{\"click\":_vm.delCarNo}},[_vm._v(\"X\")]),_c('span',{staticClass:\"delBt\",staticStyle:{\"margin-left\":\"6px\",\"width\":\"42px\",\"background-color\":\"#67c23a\",\"color\":\"#fff\"},on:{\"click\":_vm.confirm}},[_vm._v(\"确认\")]),_c('span',{staticClass:\"delBt\",staticStyle:{\"margin-left\":\"6px\",\"width\":\"42px\",\"background-color\":\"red\",\"color\":\"#fff\"},on:{\"click\":_vm.close}},[_vm._v(\"关闭\")])])],2):_vm._e()]):_vm._e()])]),_c('el-descriptions-item',{staticStyle:{\"display\":\"flex\",\"align-items\":\"center\"},attrs:{\"label\":\"载重(吨)\"}},[_c('el-tag',{style:([_vm.colorStyle]),attrs:{\"size\":\"medium\"}},[_vm._v(_vm._s(_vm.weight)+\" \")]),_c('span',{staticStyle:{\"font-size\":\"14px\",\"color\":\"red\",\"line-height\":\"36px\",\"display\":\"block\"}},[_vm._v(_vm._s(_vm.endData))])],1),_c('el-descriptions-item',{attrs:{\"label\":\"类型\"}},[_c('el-radio-group',{attrs:{\"size\":\"small\"},on:{\"input\":_vm.handleChangeType},model:{value:(_vm.carStatus),callback:function ($$v) {_vm.carStatus=$$v},expression:\"carStatus\"}},[_c('el-radio-button',{attrs:{\"label\":true}},[_vm._v(\"入场\")]),_c('el-radio-button',{staticStyle:{\"margin-left\":\"35px\"},attrs:{\"label\":false}},[_vm._v(\"出场\")])],1)],1),_c('el-descriptions-item',{attrs:{\"label\":\"抓拍照片\"}},[_c('div',{staticStyle:{\"width\":\"500px\",\"height\":\"40vh\",\"margin\":\"0 auto\"}},[(!_vm.leftUrl)?_c('img',{attrs:{\"src\":require(\"../assets/beijingtu.png\"),\"width\":\"100%\",\"height\":\"100%\",\"alt\":\"\"}}):_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"src\":_vm.leftUrl,\"preview-src-list\":[_vm.leftUrl]}})],1),_c('div',{staticStyle:{\"width\":\"500px\",\"height\":\"40vh\",\"background-color\":\"red\",\"margin\":\"0 auto\",\"margin-left\":\"20px\"}},[(!_vm.rightUrl)?_c('img',{attrs:{\"src\":require(\"../assets/beijingtu.png\"),\"width\":\"100%\",\"height\":\"100%\",\"alt\":\"\"}}):_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"100%\"},attrs:{\"src\":_vm.rightUrl,\"preview-src-list\":[_vm.rightUrl]}})],1)])],1)],1)],1),_c('el-footer',{staticStyle:{\"background-color\":\"#e9eef3\",\"padding\":\"0px 20px\"}},[_c('div',{staticClass:\"footerCard\"},[_c('el-button',{attrs:{\"type\":_vm.updateState == false ? 'success' : 'danger'},on:{\"click\":_vm.updateButton}},[_vm._v(_vm._s(_vm.updateText))]),_c('el-button',{on:{\"click\":_vm.replayget}},[_vm._v(\"重新获取\")]),_c('el-button',{attrs:{\"type\":\"primary\",\"disabled\":_vm.submitLoading},on:{\"click\":_vm.submitData}},[_vm._v(\"提交\")])],1)])],1)],1)],1),_c('el-dialog',{attrs:{\"title\":\"历史数据\",\"visible\":_vm.dialogTableVisible,\"width\":\"1200px\"},on:{\"update:visible\":function($event){_vm.dialogTableVisible=$event}}},[_c('el-form',{staticClass:\"demo-form-inline\",attrs:{\"inline\":true,\"model\":_vm.formInline},nativeOn:{\"submit\":function($event){$event.preventDefault();}}},[_c('el-form-item',{attrs:{\"label\":\"车牌号\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入车牌号\",\"size\":\"small\"},on:{\"input\":function($event){return _vm.getHistory(1)}},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\"))return null;return _vm.getHistory(1)}},model:{value:(_vm.formInline.cardNumber),callback:function ($$v) {_vm.$set(_vm.formInline, \"cardNumber\", $$v)},expression:\"formInline.cardNumber\"}})],1),_c('el-form-item',{attrs:{\"label\":\"过磅时间\"}},[_c('el-date-picker',{attrs:{\"size\":\"small\",\"value-format\":\"yyyy-MM-dd HH:mm:ss\",\"type\":\"datetimerange\",\"picker-options\":_vm.pickerOptions,\"range-separator\":\"至\",\"start-placeholder\":\"开始日期\",\"end-placeholder\":\"结束日期\",\"align\":\"right\",\"default-time\":['00:00:00', '23:59:59']},on:{\"change\":_vm.searchTime},model:{value:(_vm.guobengTime),callback:function ($$v) {_vm.guobengTime=$$v},expression:\"guobengTime\"}})],1)],1),(_vm.tableState)?_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],attrs:{\"data\":_vm.gridData,\"height\":\"55vh\",\"element-loading-text\":\"数据过多，拼命加载中...\"}},[_c('el-table-column',{attrs:{\"property\":\"carNuber\",\"label\":\"车牌号\",\"width\":\"100\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticStyle:{\"color\":\"#67c23a\",\"font-weight\":\"800\"}},[_vm._v(_vm._s(scope.row.carNumber))])]}}],null,false,3420957114)}),_c('el-table-column',{attrs:{\"property\":\"createTime\",\"label\":\"过磅时间\",\"min-width\":\"200\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('span',{staticStyle:{\"color\":\"#409eff\",\"font-weight\":\"800\"}},[_vm._v(_vm._s(scope.row.createTime))])]}}],null,false,1112449346)}),_c('el-table-column',{attrs:{\"property\":\"weight\",\"label\":\"载重量(吨)\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.weight < 10)?_c('span',{staticStyle:{\"color\":\"#e6a23c\",\"font-weight\":\"800\"}},[_vm._v(_vm._s(scope.row.weight.toFixed(3)))]):(scope.row.weight >= 40)?_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(_vm._s(scope.row.weight.toFixed(3)))]):_c('span',{staticStyle:{\"color\":\"#67c23a\",\"font-weight\":\"800\"}},[_vm._v(_vm._s(scope.row.weight))])]}}],null,false,2057303411)}),_c('el-table-column',{attrs:{\"property\":\"dataType\",\"label\":\"类型\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.dataType == 'in')?_c('el-tag',{attrs:{\"effect\":\"dark\",\"type\":\"success\"}},[_vm._v(\" \"+_vm._s(\"入场\")+\" \")]):_vm._e(),(scope.row.dataType == 'out')?_c('el-tag',{attrs:{\"effect\":\"dark\",\"type\":\"danger\"}},[_vm._v(\" \"+_vm._s(\"出场\")+\" \")]):_vm._e()]}}],null,false,3513886106)}),_c('el-table-column',{attrs:{\"property\":\"isPull\",\"label\":\"数据状态\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.isPull == '0')?_c('el-tag',{attrs:{\"effect\":\"dark\",\"type\":\"danger\"}},[_vm._v(\" \"+_vm._s(\"未上传\")+\" \")]):_vm._e(),(scope.row.isPull == '1')?_c('el-tag',{attrs:{\"effect\":\"dark\",\"type\":\"success\"}},[_vm._v(\" \"+_vm._s(\"已上传\")+\" \")]):_vm._e()]}}],null,false,9832437)}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"primary\",\"disabled\":_vm.phoneLoading},on:{\"click\":function($event){return _vm.handleEdit(scope.$index, scope.row)}}},[_vm._v(\"查看抓拍照片\")])]}}],null,false,1644244456)})],1):_vm._e(),(_vm.pageData.total > 0)?_c('el-pagination',{attrs:{\"current-page\":_vm.pageData.pageNumber,\"page-sizes\":[10, 20, 50, 100],\"page-size\":10,\"layout\":\"total, sizes, prev, pager, next, jumper\",\"total\":_vm.pageData.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}}):_vm._e()],1),_c('el-dialog',{attrs:{\"title\":\"车牌识别\",\"visible\":_vm.dialogCarVisible,\"width\":\"30%\",\"before-close\":_vm.handleClose,\"show-close\":_vm.showClose},on:{\"update:visible\":function($event){_vm.dialogCarVisible=$event}}},[_c('span',[_vm._v(\"检测到车牌\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(_vm._s(_vm.showCar))]),_vm._v(\"疑似伪造信息是否获取识别结果\")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){return _vm.carTrue('清空')}}},[_vm._v(\"清空\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.carTrue('获取')}}},[_vm._v(\"获取\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"车牌录入情况\",\"visible\":_vm.dialogCarIsVisible,\"show-close\":_vm.showClose,\"width\":\"30%\"},on:{\"update:visible\":function($event){_vm.dialogCarIsVisible=$event}}},[_c('span',[_vm._v(\"检测到车牌\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(_vm._s(_vm.showCar))]),_vm._v(\"未录入系统\")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.CarisFalse}},[_vm._v(\"确定\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"提示\",\"visible\":_vm.dialogVisibleOne,\"width\":\"50%\",\"before-close\":_vm.handleCloseOne},on:{\"update:visible\":function($event){_vm.dialogVisibleOne=$event}}},[_c('span',[_vm._v(\"系统根据车辆载重检测出本次提交数据可能是\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(\"出场数据\")]),_vm._v(\",请确认提交数据是否准确!\")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){_vm.dialogVisibleOne = false}}},[_vm._v(\"确 定\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"提示\",\"visible\":_vm.dialogVisibleTwe,\"width\":\"50%\",\"before-close\":_vm.handleCloseTwe},on:{\"update:visible\":function($event){_vm.dialogVisibleTwe=$event}}},[_c('span',[_vm._v(\"系统根据车辆载重检测出本次提交数据可能是\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(\"出场数据\")]),_vm._v(\",请\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(\"再次\")]),_vm._v(\"确认提交数据是否准确!!!\")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){_vm.dialogVisibleTwe = false}}},[_vm._v(\"确 定\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"提示\",\"visible\":_vm.dialogVisibleThree,\"width\":\"50%\",\"before-close\":_vm.handleCloseOne},on:{\"update:visible\":function($event){_vm.dialogVisibleThree=$event}}},[_c('span',[_vm._v(\"系统根据车辆载重检测出本次提交数据可能是\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(\"入场数据\")]),_vm._v(\",请确认提交数据是否准确!\")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){_vm.dialogVisibleThree = false}}},[_vm._v(\"确 定\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"提示\",\"visible\":_vm.dialogVisibleFore,\"width\":\"50%\",\"before-close\":_vm.handleCloseTwe},on:{\"update:visible\":function($event){_vm.dialogVisibleFore=$event}}},[_c('span',[_vm._v(\"系统根据车辆载重检测出本次提交数据可能是\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(\"入场数据\")]),_vm._v(\",请\"),_c('span',{staticStyle:{\"color\":\"red\",\"font-weight\":\"800\"}},[_vm._v(\"再次\")]),_vm._v(\"确认提交数据是否准确!!!\")]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){_vm.dialogVisibleFore = false}}},[_vm._v(\"确 定\")])],1)]),(_vm.centerDialogVisible)?_c('el-dialog',{attrs:{\"title\":\"抓拍照片详情内容\",\"visible\":_vm.centerDialogVisible,\"width\":\"80%\",\"top\":\"5vh\",\"custom-class\":\"image-dialog\"},on:{\"update:visible\":function($event){_vm.centerDialogVisible=$event}}},[_c('div',{staticClass:\"image-container\"},[_c('div',{staticClass:\"image-wrapper\"},[_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"500px\"},attrs:{\"src\":_vm.urlimagL,\"preview-src-list\":_vm.srcList}},[_c('div',{staticClass:\"image-slot\"},[_vm._v(\"加载中\"),_c('span',{staticClass:\"dota\"},[_vm._v(\"...\")])])]),_c('div',{staticClass:\"image-caption\"},[_vm._v(\"抓拍照片-1\")])],1),_c('div',{staticClass:\"image-wrapper\"},[_c('el-image',{staticStyle:{\"width\":\"100%\",\"height\":\"500px\"},attrs:{\"src\":_vm.urlimagR,\"preview-src-list\":_vm.srcList}},[_c('div',{staticClass:\"image-slot\"},[_vm._v(\"加载中\"),_c('span',{staticClass:\"dota\"},[_vm._v(\"...\")])])]),_c('div',{staticClass:\"image-caption\"},[_vm._v(\"抓拍照片-2\")])],1)]),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.centerDialogVisible = false}}},[_vm._v(\"关闭\")])],1),_c('div',{staticClass:\"demo-image__preview\"})]):_vm._e()],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div id=\"app\">\r\n    <el-container style=\"overflow: hidden\">\r\n      <el-header\r\n        style=\"\r\n          text-shadow: 0 10px 10px rgba(0, 0, 0, 0.2);\r\n          color: #fff;\r\n          font-size: 40px;\r\n          font-weight: bold;\r\n          font-style: italic;\r\n        \"\r\n        >河北省采(弃)砂项目</el-header\r\n      >\r\n      <el-container>\r\n        <el-aside width=\"600px\" style=\"overflow: hidden; height: 100%\">\r\n          <el-card class=\"box-card1\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <!-- <button id=\"fullscreen-btn\" @click=\"fullscreenBig()\">全屏</button> -->\r\n              <el-tooltip\r\n                class=\"item\"\r\n                effect=\"dark\"\r\n                content=\"刷新页面\"\r\n                placement=\"bottom-end\"\r\n              >\r\n                <el-button\r\n                  type=\"success\"\r\n                  icon=\"el-icon-refresh-right\"\r\n                  circle\r\n                  size=\"mini\"\r\n                  style=\"margin-right: 20px\"\r\n                  @click=\"SX\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <el-tooltip\r\n                class=\"item\"\r\n                effect=\"dark\"\r\n                content=\"触发抬杆\"\r\n                placement=\"bottom-end\"\r\n              >\r\n                <el-button\r\n                  type=\"warning\"\r\n                  icon=\"el-icon-smoking\"\r\n                  circle\r\n                  size=\"mini\"\r\n                  style=\"margin-right: 20px\"\r\n                  @click=\"TG\"\r\n                ></el-button>\r\n              </el-tooltip>\r\n              <span style=\"text-align: left; font-weight: 900; font-size: 18px\"\r\n                >实时监控</span\r\n              >\r\n              <!-- <span style=\"text-align: left; margin-left: 20px\" v-if=\"shebei1Status\">设备一:<span\r\n                  style=\"color: red; font-size: 14px\">(连接失败)</span></span>\r\n              <span style=\"text-align: left; margin-left: 20px\" v-if=\"shebei2Status\">设备二:<span\r\n                  style=\"color: red; font-size: 14px\">(连接失败)</span></span> -->\r\n            </div>\r\n            <!-- 第一个监控视频 -->\r\n            <div\r\n              style=\"\r\n                width: 550px;\r\n                height: 35vh;\r\n                margin: 0 auto;\r\n                margin-bottom: 50px;\r\n                margin-top: 20px;\r\n                position: relative;\r\n                background-image: url(./assets/beijingtu.png);\r\n              \"\r\n            >\r\n              <video\r\n                ref=\"videoElement\"\r\n                width=\"100%\"\r\n                height=\"100%\"\r\n                id=\"videoEl\"\r\n              ></video>\r\n              <div style=\"position: absolute; bottom: 0; left: 0; z-index: 999\">\r\n                <el-button\r\n                  icon=\"el-icon-video-play\"\r\n                  type=\"info\"\r\n                  size=\"mini\"\r\n                  circle\r\n                  @click=\"flv_start\"\r\n                  v-if=\"buttonStatus == false\"\r\n                  style=\"margin-left: 10px; margin-top: 15px\"\r\n                ></el-button>\r\n                <el-button\r\n                  style=\"\r\n                    margin-left: 10px;\r\n                    margin-top: 15px;\r\n                    position: relative;\r\n                    z-index: 99999;\r\n                  \"\r\n                  v-if=\"buttonStatus == true\"\r\n                  icon=\"el-icon-video-pause\"\r\n                  size=\"mini\"\r\n                  circle\r\n                  @click=\"flv_end\"\r\n                  type=\"success\"\r\n                ></el-button>\r\n                <el-button\r\n                  icon=\"el-icon-full-screen\"\r\n                  size=\"mini\"\r\n                  circle\r\n                  @click=\"flv_full\"\r\n                  style=\"left: 0; position: relative; z-index: 999999\"\r\n                ></el-button>\r\n              </div>\r\n            </div>\r\n            <!-- 第二个监控视频 -->\r\n            <div\r\n              style=\"\r\n                width: 550px;\r\n                height: 35vh;\r\n                margin: 0 auto;\r\n                margin-bottom: 70px;\r\n                margin-top: 20px;\r\n                position: relative;\r\n                background-image: url(../assets/beijingtu.png);\r\n              \"\r\n            >\r\n              <video\r\n                ref=\"videoElement2\"\r\n                width=\"100%\"\r\n                height=\"100%\"\r\n                id=\"videoE2\"\r\n              ></video>\r\n              <div style=\"position: absolute; bottom: 0; left: 0; z-index: 999\">\r\n                <el-button\r\n                  icon=\"el-icon-video-play\"\r\n                  type=\"info\"\r\n                  size=\"mini\"\r\n                  circle\r\n                  @click=\"flv_start2\"\r\n                  v-if=\"buttonStatus2 == false\"\r\n                  style=\"margin-left: 10px; margin-top: 15px\"\r\n                ></el-button>\r\n                <el-button\r\n                  style=\"\r\n                    margin-left: 10px;\r\n                    margin-top: 15px;\r\n                    position: relative;\r\n                    z-index: 99999;\r\n                  \"\r\n                  v-if=\"buttonStatus2 == true\"\r\n                  icon=\"el-icon-video-pause\"\r\n                  size=\"mini\"\r\n                  circle\r\n                  @click=\"flv_end2\"\r\n                  type=\"success\"\r\n                ></el-button>\r\n                <el-button\r\n                  icon=\"el-icon-full-screen\"\r\n                  size=\"mini\"\r\n                  circle\r\n                  @click=\"flv_full2\"\r\n                  style=\"left: 0; position: relative; z-index: 999999\"\r\n                ></el-button>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-aside>\r\n        <el-container>\r\n          <el-main style=\"overflow: hidden\">\r\n            <el-card class=\"box-card\" style=\"height: 84vh\">\r\n              <div slot=\"header\" class=\"clearfix\">\r\n                <span\r\n                  style=\"text-align: left; font-weight: 900; font-size: 18px\"\r\n                  >车辆信息-<span\r\n                    :class=\"\r\n                      carStatus == false\r\n                        ? 'carNoBoxInputTrue'\r\n                        : 'carNoBoxInputFalse'\r\n                    \"\r\n                    >{{ carStatus == false ? \"出场\" : \"入场\" }}</span\r\n                  ></span\r\n                >\r\n                <el-button\r\n                  type=\"success\"\r\n                  size=\"small \"\r\n                  round\r\n                  style=\"float: right\"\r\n                  @click=\"showHistory\"\r\n                  >历史数据</el-button\r\n                >\r\n              </div>\r\n\r\n              <!-- <div style=\"width:550px;height:300px;background-color:red;margin:0 auto;margin-bottom:100px\"></div> -->\r\n              <el-descriptions title=\"信息展示\" :column=\"1\">\r\n                <el-descriptions-item label=\"项目名称\">\r\n                  <el-tag size=\"medium\">{{\r\n                    ClientData.client.projectName\r\n                  }}</el-tag></el-descriptions-item\r\n                >\r\n                <el-descriptions-item label=\"磅站名称\">\r\n                  <el-tag size=\"medium\">{{\r\n                    ClientData.client.stationName\r\n                  }}</el-tag>\r\n                  <!-- <el-select\r\n                    v-model=\"stationId\"\r\n                    clearable\r\n                    placeholder=\"请选择泵站信息\"\r\n                    size=\"mini\">\r\n                    <el-option\r\n                      v-for=\"item in weightInfo\"\r\n                      :key=\"item.stationId\"\r\n                      :label=\"item.stationName\"\r\n                      :value=\"item.stationId\">\r\n                    </el-option>\r\n                  </el-select> -->\r\n                </el-descriptions-item>\r\n                <!-- 车牌号 -->\r\n                <el-descriptions-item label=\"车牌号\">\r\n                  <div style=\"text-align: center\">\r\n                    <div\r\n                      :class=\"\r\n                        licensePlateUnit.length <= 7\r\n                          ? 'carNoBoxInput'\r\n                          : 'carNoBoxInput1'\r\n                      \"\r\n                    >\r\n                      <div\r\n                        style=\"\r\n                          padding: 6px;\r\n                          border: 2px solid #fff;\r\n                          border-radius: 6px;\r\n                          margin: 6px 3px 6px 6px;\r\n                        \"\r\n                      >\r\n                        <input\r\n                          class=\"inputBox\"\r\n                          :value=\"licensePlateUnit[0]\"\r\n                          ref=\"inputBox0\"\r\n                          @click=\"licensePlateDoor = true\"\r\n                          @change=\"handleSelect\"\r\n                        />\r\n                        <input\r\n                          class=\"inputBox\"\r\n                          :value=\"licensePlateUnit[1]\"\r\n                          @click=\"licensePlateDoor = true\"\r\n                          @change=\"license1\"\r\n                        />\r\n                        <span class=\"dot\"></span>\r\n                        <input\r\n                          class=\"inputBox\"\r\n                          :value=\"licensePlateUnit[2]\"\r\n                          @click=\"licensePlateDoor = true\"\r\n                          @change=\"license2\"\r\n                        />\r\n                        <input\r\n                          class=\"inputBox\"\r\n                          :value=\"licensePlateUnit[3]\"\r\n                          @click=\"licensePlateDoor = true\"\r\n                          @change=\"license3\"\r\n                        />\r\n                        <input\r\n                          class=\"inputBox\"\r\n                          :value=\"licensePlateUnit[4]\"\r\n                          @click=\"licensePlateDoor = true\"\r\n                          @change=\"license4\"\r\n                        />\r\n                        <input\r\n                          class=\"inputBox\"\r\n                          :value=\"licensePlateUnit[5]\"\r\n                          @click=\"licensePlateDoor = true\"\r\n                          @change=\"license5\"\r\n                        />\r\n                        <input\r\n                          class=\"inputBox\"\r\n                          :value=\"licensePlateUnit[6]\"\r\n                          @click=\"licensePlateDoor = true\"\r\n                          @change=\"license6\"\r\n                        />\r\n                        <input\r\n                          v-if=\"7 === licensePlateUnit.length - 1\"\r\n                          class=\"inputBox\"\r\n                          @change=\"license7\"\r\n                          :class=\"\r\n                            7 === licensePlateUnit.length - 1\r\n                              ? 'inputBoxActive'\r\n                              : 'inputBox'\r\n                          \"\r\n                          :value=\"licensePlateUnit[7]\"\r\n                        />\r\n                        <img\r\n                          v-if=\"7 !== licensePlateUnit.length - 1\"\r\n                          src=\"../assets/yezi2.png\"\r\n                          style=\"height: 34px; width: 34px\"\r\n                          alt=\"新能源\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                    <!-- 车牌号模糊搜素 -->\r\n                    <ul\r\n                      v-if=\"licensePlateDoor == true\"\r\n                      style=\"\r\n                        background-color: red;\r\n                        display: flex;\r\n                        width: 600px;\r\n                        height: 36px;\r\n                        line-height: 36px;\r\n                        background: #d0d5d9;\r\n                        position: absolute;\r\n                        left: 630px;\r\n                        z-index: 4;\r\n                      \"\r\n                    >\r\n                      <li\r\n                        v-for=\"result in firstSixItems\"\r\n                        :key=\"result\"\r\n                        @click=\"handleClick(result)\"\r\n                        style=\"\r\n                          width: 100px;\r\n                          list-style-type: none;\r\n                          height: 36px;\r\n                          line-height: 36px;\r\n                          color: #fff;\r\n                          font-weight: bold;\r\n                          text-align: center;\r\n                        \"\r\n                      >\r\n                        {{ result }}\r\n                      </li>\r\n                    </ul>\r\n                    <!-- 小键盘 -->\r\n                    <div\r\n                      v-if=\"licensePlateDoor == true\"\r\n                      style=\"\r\n                        position: absolute;\r\n                        top: 410px;\r\n                        left: 630px;\r\n                        width: 100%;\r\n                        z-index: 999;\r\n                      \"\r\n                    >\r\n                      <div v-if=\"licensePlateUnitLength <= 0\" class=\"carNoBox\">\r\n                        <span\r\n                          class=\"carNo\"\r\n                          v-for=\"item in columns\"\r\n                          :key=\"item\"\r\n                          @click=\"pickOn(item)\"\r\n                        >\r\n                          {{ item }}\r\n                        </span>\r\n                        <span class=\"delBt\" @click=\"delCarNo\">X</span>\r\n                      </div>\r\n                      <div v-if=\"licensePlateUnitLength >= 1\" class=\"carNoBox\">\r\n                        <span\r\n                          class=\"carNo\"\r\n                          v-for=\"item in numberColumns\"\r\n                          :key=\"item\"\r\n                          @click=\"pickOn(item)\"\r\n                        >\r\n                          {{ item }}\r\n                        </span>\r\n                        <div\r\n                          style=\"\r\n                            display: flex;\r\n                            align-items: center;\r\n                            text-align: center;\r\n                          \"\r\n                        >\r\n                          <span\r\n                            class=\"delBt\"\r\n                            @click=\"delCarNo\"\r\n                            style=\"text-align: center\"\r\n                            >X</span\r\n                          >\r\n                          <span\r\n                            class=\"delBt\"\r\n                            style=\"\r\n                              margin-left: 6px;\r\n                              width: 42px;\r\n                              background-color: #67c23a;\r\n                              color: #fff;\r\n                            \"\r\n                            @click=\"confirm\"\r\n                            >确认</span\r\n                          >\r\n                          <span\r\n                            class=\"delBt\"\r\n                            style=\"\r\n                              margin-left: 6px;\r\n                              width: 42px;\r\n                              background-color: red;\r\n                              color: #fff;\r\n                            \"\r\n                            @click=\"close\"\r\n                            >关闭</span\r\n                          >\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </el-descriptions-item>\r\n                <!-- 载重 -->\r\n                <el-descriptions-item\r\n                  label=\"载重(吨)\"\r\n                  style=\"display: flex; align-items: center\"\r\n                >\r\n                  <!-- <el-tag size=\"small\">0.25(吨)</el-tag> -->\r\n                  <el-tag size=\"medium\" :style=\"[colorStyle]\"\r\n                    >{{ weight }}\r\n                  </el-tag>\r\n                  <span\r\n                    style=\"\r\n                      font-size: 14px;\r\n                      color: red;\r\n                      line-height: 36px;\r\n                      display: block;\r\n                    \"\r\n                    >{{ endData }}</span\r\n                  >\r\n                  <!-- <span v-if=\"weight\">{{ weight }}</span> -->\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"类型\">\r\n                  <!-- <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\">入场</el-button>\r\n                  <el-button type=\"primary\" icon=\"el-icon-search\" size=\"small\">出场</el-button> -->\r\n                  <!-- <el-switch style=\"display: block\" v-model=\"carStatus\" @change=\"handleChangeType\"\r\n                    active-color=\"#13ce66\" inactive-color=\"#ff4949\" active-text=\"入场\" inactive-text=\"出场\">\r\n                  </el-switch> -->\r\n                  <el-radio-group\r\n                    v-model=\"carStatus\"\r\n                    @input=\"handleChangeType\"\r\n                    size=\"small\"\r\n                  >\r\n                    <el-radio-button :label=\"true\" style=\"\"\r\n                      >入场</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"false\" style=\"margin-left: 35px\"\r\n                      >出场</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                  <!-- <el-button type=\"primary\" plain size=\"small\" @click=\"handleChangeType(true)\"\r\n                   >入场</el-button>\r\n                  <el-button type=\"primary\" plain size=\"small\" @click=\"handleChangeType(false)\"\r\n                    >出场</el-button> -->\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"抓拍照片\">\r\n                  <div style=\"width: 500px; height: 40vh; margin: 0 auto\">\r\n                    <img\r\n                      src=\"../assets/beijingtu.png\"\r\n                      width=\"100%\"\r\n                      height=\"100%\"\r\n                      alt=\"\"\r\n                      v-if=\"!leftUrl\"\r\n                    />\r\n                    <!-- <img :src=\"leftUrl\" width=\"100%\" height=\"100%\" alt=\"\" v-if=\"leftUrl\"> -->\r\n                    <el-image\r\n                      style=\"width: 100%; height: 100%\"\r\n                      :src=\"leftUrl\"\r\n                      :preview-src-list=\"[leftUrl]\"\r\n                      v-else\r\n                    >\r\n                    </el-image>\r\n                  </div>\r\n                  <div\r\n                    style=\"\r\n                      width: 500px;\r\n                      height: 40vh;\r\n                      background-color: red;\r\n                      margin: 0 auto;\r\n                      margin-left: 20px;\r\n                    \"\r\n                  >\r\n                    <img\r\n                      src=\"../assets/beijingtu.png\"\r\n                      width=\"100%\"\r\n                      height=\"100%\"\r\n                      alt=\"\"\r\n                      v-if=\"!rightUrl\"\r\n                    />\r\n                    <el-image\r\n                      style=\"width: 100%; height: 100%\"\r\n                      :src=\"rightUrl\"\r\n                      :preview-src-list=\"[rightUrl]\"\r\n                      v-else\r\n                    >\r\n                    </el-image>\r\n                  </div>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-card>\r\n          </el-main>\r\n          <el-footer style=\"background-color: #e9eef3; padding: 0px 20px\">\r\n            <div class=\"footerCard\">\r\n              <el-button\r\n                :type=\"updateState == false ? 'success' : 'danger'\"\r\n                @click=\"updateButton\"\r\n                >{{ updateText }}</el-button\r\n              >\r\n              <el-button @click=\"replayget\">重新获取</el-button>\r\n\r\n              <el-button\r\n                type=\"primary\"\r\n                @click=\"submitData\"\r\n                :disabled=\"submitLoading\"\r\n                >提交</el-button\r\n              >\r\n              <!-- <el-button type=\"success\" @click=\"handle_Taigan\">抬杆</el-button> -->\r\n            </div>\r\n          </el-footer>\r\n        </el-container>\r\n      </el-container>\r\n    </el-container>\r\n    <el-dialog\r\n      title=\"历史数据\"\r\n      :visible.sync=\"dialogTableVisible\"\r\n      width=\"1200px\"\r\n    >\r\n      <el-form\r\n        :inline=\"true\"\r\n        :model=\"formInline\"\r\n        class=\"demo-form-inline\"\r\n        @submit.native.prevent\r\n      >\r\n        <el-form-item label=\"车牌号\">\r\n          <el-input\r\n            v-model=\"formInline.cardNumber\"\r\n            placeholder=\"请输入车牌号\"\r\n            @keyup.enter.native=\"getHistory(1)\"\r\n            @input=\"getHistory(1)\"\r\n            size=\"small\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"过磅时间\">\r\n          <el-date-picker\r\n            size=\"small\"\r\n            v-model=\"guobengTime\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            @change=\"searchTime\"\r\n            type=\"datetimerange\"\r\n            :picker-options=\"pickerOptions\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            align=\"right\"\r\n            :default-time=\"['00:00:00', '23:59:59']\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table\r\n        :data=\"gridData\"\r\n        v-if=\"tableState\"\r\n        height=\"55vh\"\r\n        v-loading=\"loading\"\r\n        element-loading-text=\"数据过多，拼命加载中...\"\r\n      >\r\n        <el-table-column property=\"carNuber\" label=\"车牌号\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #67c23a; font-weight: 800\">{{\r\n              scope.row.carNumber\r\n            }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          property=\"createTime\"\r\n          label=\"过磅时间\"\r\n          min-width=\"200\"\r\n          align=\"center\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <span style=\"color: #409eff; font-weight: 800\">{{\r\n              scope.row.createTime\r\n            }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column property=\"weight\" label=\"载重量(吨)\">\r\n          <template slot-scope=\"scope\">\r\n            <span\r\n              style=\"color: #e6a23c; font-weight: 800\"\r\n              v-if=\"scope.row.weight < 10\"\r\n              >{{ scope.row.weight.toFixed(3) }}</span\r\n            >\r\n            <span\r\n              style=\"color: red; font-weight: 800\"\r\n              v-else-if=\"scope.row.weight >= 40\"\r\n              >{{ scope.row.weight.toFixed(3) }}</span\r\n            >\r\n            <span style=\"color: #67c23a; font-weight: 800\" v-else>{{\r\n              scope.row.weight\r\n            }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column property=\"dataType\" label=\"类型\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag\r\n              effect=\"dark\"\r\n              type=\"success\"\r\n              v-if=\"scope.row.dataType == 'in'\"\r\n            >\r\n              {{ \"入场\" }}\r\n            </el-tag>\r\n            <el-tag\r\n              effect=\"dark\"\r\n              type=\"danger\"\r\n              v-if=\"scope.row.dataType == 'out'\"\r\n            >\r\n              {{ \"出场\" }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column property=\"isPull\" label=\"数据状态\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag effect=\"dark\" type=\"danger\" v-if=\"scope.row.isPull == '0'\">\r\n              {{ \"未上传\" }}\r\n            </el-tag>\r\n            <el-tag effect=\"dark\" type=\"success\" v-if=\"scope.row.isPull == '1'\">\r\n              {{ \"已上传\" }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <!-- <el-table-column property=\"headImage\" label=\"抓拍-1\">\r\n          <template v-slot=\"{ row }\">\r\n           \r\n            <el-image\r\n              style=\"width: 50px; height: 50px\"\r\n              :src=\"row.headImage\"\r\n              :preview-src-list=\"[row.headImage]\"\r\n            >\r\n            </el-image>\r\n          </template>\r\n        </el-table-column> -->\r\n        <!-- <el-table-column property=\"rearImage\" label=\"抓拍-2\">\r\n          <template v-slot=\"{ row }\">\r\n           \r\n            <el-image\r\n              style=\"width: 50px; height: 50px\"\r\n              :src=\"row.rearImage\"\r\n              :preview-src-list=\"[row.rearImage]\"\r\n            >\r\n            </el-image>\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column label=\"操作\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              type=\"primary\"\r\n              @click=\"handleEdit(scope.$index, scope.row)\"\r\n              :disabled=\"phoneLoading\"\r\n              >查看抓拍照片</el-button\r\n            >\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pageData.total > 0\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageData.pageNumber\"\r\n        :page-sizes=\"[10, 20, 50, 100]\"\r\n        :page-size=\"10\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        :total=\"pageData.total\"\r\n      >\r\n      </el-pagination>\r\n    </el-dialog>\r\n    <!-- 伪造车牌识别 -->\r\n    <el-dialog\r\n      title=\"车牌识别\"\r\n      :visible.sync=\"dialogCarVisible\"\r\n      width=\"30%\"\r\n      :before-close=\"handleClose\"\r\n      :show-close=\"showClose\"\r\n    >\r\n      <span\r\n        >检测到车牌<span style=\"color: red; font-weight: 800\">{{\r\n          showCar\r\n        }}</span\r\n        >疑似伪造信息是否获取识别结果</span\r\n      >\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"carTrue('清空')\">清空</el-button>\r\n        <el-button type=\"primary\" @click=\"carTrue('获取')\">获取</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"车牌录入情况\"\r\n      :visible.sync=\"dialogCarIsVisible\"\r\n      :show-close=\"showClose\"\r\n      width=\"30%\"\r\n    >\r\n      <span\r\n        >检测到车牌<span style=\"color: red; font-weight: 800\">{{\r\n          showCar\r\n        }}</span\r\n        >未录入系统</span\r\n      >\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"CarisFalse\">确定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 第一次单击提交按钮 -出场-->\r\n    <el-dialog\r\n      title=\"提示\"\r\n      :visible.sync=\"dialogVisibleOne\"\r\n      width=\"50%\"\r\n      :before-close=\"handleCloseOne\"\r\n    >\r\n      <span\r\n        >系统根据车辆载重检测出本次提交数据可能是<span\r\n          style=\"color: red; font-weight: 800\"\r\n          >出场数据</span\r\n        >,请确认提交数据是否准确!</span\r\n      >\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <!-- <el-button @click=\"dialogVisibleOne = false\">取 消</el-button> -->\r\n        <el-button type=\"primary\" @click=\"dialogVisibleOne = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n    <!--  第二次点击提交按钮-出场-->\r\n    <el-dialog\r\n      title=\"提示\"\r\n      :visible.sync=\"dialogVisibleTwe\"\r\n      width=\"50%\"\r\n      :before-close=\"handleCloseTwe\"\r\n    >\r\n      <span\r\n        >系统根据车辆载重检测出本次提交数据可能是<span\r\n          style=\"color: red; font-weight: 800\"\r\n          >出场数据</span\r\n        >,请<span style=\"color: red; font-weight: 800\">再次</span\r\n        >确认提交数据是否准确!!!</span\r\n      >\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <!-- <el-button @click=\"dialogVisible = false\">取 消</el-button> -->\r\n        <el-button type=\"primary\" @click=\"dialogVisibleTwe = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 第一次单击提交按钮 -入场-->\r\n    <el-dialog\r\n      title=\"提示\"\r\n      :visible.sync=\"dialogVisibleThree\"\r\n      width=\"50%\"\r\n      :before-close=\"handleCloseOne\"\r\n    >\r\n      <span\r\n        >系统根据车辆载重检测出本次提交数据可能是<span\r\n          style=\"color: red; font-weight: 800\"\r\n          >入场数据</span\r\n        >,请确认提交数据是否准确!</span\r\n      >\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <!-- <el-button @click=\"dialogVisibleOne = false\">取 消</el-button> -->\r\n        <el-button type=\"primary\" @click=\"dialogVisibleThree = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n    <!--  第二次点击提交按钮 -入场-->\r\n    <el-dialog\r\n      title=\"提示\"\r\n      :visible.sync=\"dialogVisibleFore\"\r\n      width=\"50%\"\r\n      :before-close=\"handleCloseTwe\"\r\n    >\r\n      <span\r\n        >系统根据车辆载重检测出本次提交数据可能是<span\r\n          style=\"color: red; font-weight: 800\"\r\n          >入场数据</span\r\n        >,请<span style=\"color: red; font-weight: 800\">再次</span\r\n        >确认提交数据是否准确!!!</span\r\n      >\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <!-- <el-button @click=\"dialogVisible = false\">取 消</el-button> -->\r\n        <el-button type=\"primary\" @click=\"dialogVisibleFore = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n    <!-- 车辆照片弹框 -->\r\n    <el-dialog\r\n      v-if=\"centerDialogVisible\"\r\n      title=\"抓拍照片详情内容\"\r\n      :visible.sync=\"centerDialogVisible\"\r\n      width=\"80%\"\r\n      top=\"5vh\"\r\n      custom-class=\"image-dialog\"\r\n    >\r\n      <div class=\"image-container\">\r\n        <div class=\"image-wrapper\">\r\n          <el-image\r\n            style=\"width: 100%; height: 500px\"\r\n            :src=\"urlimagL\"\r\n            :preview-src-list=\"srcList\"\r\n          >\r\n            <div class=\"image-slot\">加载中<span class=\"dota\">...</span></div>\r\n          </el-image>\r\n          <div class=\"image-caption\">抓拍照片-1</div>\r\n        </div>\r\n\r\n        <div class=\"image-wrapper\">\r\n          <el-image\r\n            style=\"width: 100%; height: 500px\"\r\n            :src=\"urlimagR\"\r\n            :preview-src-list=\"srcList\"\r\n          >\r\n            <div class=\"image-slot\">加载中<span class=\"dota\">...</span></div>\r\n          </el-image>\r\n          <div class=\"image-caption\">抓拍照片-2</div>\r\n        </div>\r\n      </div>\r\n\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"centerDialogVisible = false\">关闭</el-button>\r\n      </span>\r\n\r\n      <div class=\"demo-image__preview\"></div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport dayjs from \"dayjs\";\r\nimport axios from \"axios\";\r\nimport flvjs from \"flv.js\";\r\nimport mqtt from \"mqtt\";\r\nimport CryptoJS from \"crypto-js\";\r\nconst key = CryptoJS.enc.Utf8.parse(\"1234567890123456\"); // 这里只是示例，实际需要符合 AES 要求的密钥长度\r\nconst iv = \"1234567890123456\"; // 初始化向量，长度为16字节\r\nexport default {\r\n  name: \"App\",\r\n  components: {},\r\n  data() {\r\n    const today = new Date();\r\n    // 开始时间：当天 00:00:00\r\n    const startTime = new Date(today.setHours(0, 0, 0, 0));\r\n    // 结束时间：当天 23:59:59（或次日 00:00:00）\r\n    const endTime = new Date(today.setHours(23, 59, 59, 999));\r\n    // 如果需严格 24:00:00，可用次日 00:00:00（逻辑上等价）\r\n    // const endTime = new Date(today.setDate(today.getDate() + 1));\r\n    // endTime.setHours(0, 0, 0, 0);\r\n    const localDate = dayjs(startTime).format(\"YYYY-MM-DD HH:mm:ss\");\r\n    const localDate1 = dayjs(endTime).format(\"YYYY-MM-DD HH:mm:ss\");\r\n    return {\r\n      tgid: \"\",\r\n      tgId: \"\",\r\n      cremarsId: [],\r\n      colorStyle: {\r\n        fontSize: \"30px\",\r\n      },\r\n      weightErrorInfoNum: 0,\r\n      endDatas: \"\",\r\n      phoneLoading: false,\r\n      urlimagL: null,\r\n      urlimagR: null,\r\n      srcList: [],\r\n      centerDialogVisible: false,\r\n\r\n      ClientData: {\r\n        client: {\r\n          projectName: \"前端测试\",\r\n          stationName: \"前端测试\",\r\n        },\r\n      },\r\n      loading: false,\r\n      guobengTime: [localDate, localDate1],\r\n\r\n      pickerOptions: {\r\n        shortcuts: [\r\n          {\r\n            text: \"最近一周\",\r\n            onClick(picker) {\r\n              const end = new Date();\r\n              const start = new Date();\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\r\n              picker.$emit(\"pick\", [start, end]);\r\n            },\r\n          },\r\n          {\r\n            text: \"最近一个月\",\r\n            onClick(picker) {\r\n              const end = new Date();\r\n              const start = new Date();\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\r\n              picker.$emit(\"pick\", [start, end]);\r\n            },\r\n          },\r\n          {\r\n            text: \"最近三个月\",\r\n            onClick(picker) {\r\n              const end = new Date();\r\n              const start = new Date();\r\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\r\n              picker.$emit(\"pick\", [start, end]);\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      client3: null,\r\n      client2: null,\r\n      client: null,\r\n      pageData: {\r\n        pageNumber: 1,\r\n        pageSize: 10,\r\n        total: 0,\r\n      },\r\n      showClose: false,\r\n      stationId: \"\", //选择的泵站id\r\n      weightInfo: [],\r\n      weight: 0, //重量\r\n      showCar: \"\", //伪车牌号\r\n      carIs: false, //车牌是否存在\r\n      requestToken: \"\",\r\n      reader: null, // 用于读取数据的 reader\r\n      port: null,\r\n      writer: null, // 添加一个 writer 属性来保存 WritableStreamDefaultWriter 实例\r\n      receivedData: \"\",\r\n      errorMessage: \"\",\r\n      payload: null,\r\n      dialogCarVisible: false,\r\n      dialogCarIsVisible: false,\r\n      shebei1Status: false,\r\n      shebei2Status: false,\r\n      xintiaobao: \"\",\r\n      xintiaobao2: \"\",\r\n      submitLoading: false,\r\n      videoEl: null,\r\n      videoE2: null,\r\n      loading: true,\r\n      carNum: \"\",\r\n      carNumPassword: \"\",\r\n      carStatus: true, //如果是入场的话为 true 出场为false\r\n      tableState: false,\r\n      // leftUrl:'',\r\n      buttonStatus: false,\r\n      leftUrl: \"\",\r\n      rightUrl: \"\",\r\n      updateText: \"开始采集\",\r\n      updateState: false,\r\n      buttonStatus2: false,\r\n      flvPlayer: null,\r\n      flvPlayer2: null,\r\n      equimentId1: \"61d41d31-6aa5984e\",\r\n      equimentId2: \"3c294c0b-19b7fb36\",\r\n      equimentId3: \"3c294c0b-19b7fb32\",\r\n      // baseUrl: \"http://*************:8077\",\r\n      baseUrl: \"http://127.0.0.1:8077\",\r\n      mqttIp:\"127.0.0.1\",\r\n      // mqttIp:'*************',\r\n      licensePlate: \"\",\r\n      gridData: [],\r\n      formInline: {\r\n        cardNumber: \"\",\r\n        startTime: localDate,\r\n        endTime: localDate1,\r\n      },\r\n      searchResults: [\r\n        \"京A12345\",\r\n        \"京A12346\",\r\n        \"京A12347\",\r\n        \"京A12348\",\r\n        \"京A12349\",\r\n        \"京A12350\",\r\n      ],\r\n      dialogTableVisible: false,\r\n      licensePlateDoor: false,\r\n      activeIndex: 0,\r\n      licensePlateUnit: [],\r\n      columns: [\r\n        //省缩写选择\r\n        \"京\",\r\n        \"沪\",\r\n        \"鄂\",\r\n        \"湘\",\r\n        \"川\",\r\n        \"渝\",\r\n        \"粤\",\r\n        \"闽\",\r\n        \"晋\",\r\n        \"黑\",\r\n        \"津\",\r\n        \"浙\",\r\n        \"豫\",\r\n        \"赣\",\r\n        \"贵\",\r\n        \"青\",\r\n        \"琼\",\r\n        \"宁\",\r\n        \"吉\",\r\n        \"蒙\",\r\n        \"冀\",\r\n        \"苏\",\r\n        \"皖\",\r\n        \"桂\",\r\n        \"云\",\r\n        \"陕\",\r\n        \"甘\",\r\n        \"藏\",\r\n        \"新\",\r\n        \"辽\",\r\n        \"鲁\",\r\n      ],\r\n      numberColumns: [\r\n        \"1\",\r\n        \"2\",\r\n        \"3\",\r\n        \"4\",\r\n        \"5\",\r\n        \"6\",\r\n        \"7\",\r\n        \"8\",\r\n        \"9\",\r\n        \"0\",\r\n        \"Q\",\r\n        \"W\",\r\n        \"E\",\r\n        \"R\",\r\n        \"T\",\r\n        \"Y\",\r\n        \"U\",\r\n        \"I\",\r\n        \"O\",\r\n        \"P\",\r\n        \"A\",\r\n        \"S\",\r\n        \"D\",\r\n        \"F\",\r\n        \"G\",\r\n        \"H\",\r\n        \"J\",\r\n        \"K\",\r\n        \"L\",\r\n        \"Z\",\r\n        \"X\",\r\n        \"C\",\r\n        \"V\",\r\n        \"B\",\r\n        \"N\",\r\n        \"M\",\r\n        \"港\",\r\n        \"澳\",\r\n        \"学\",\r\n        \"领\",\r\n        \"警\",\r\n      ],\r\n      initData: \"\",\r\n      // ClientData: \"\",\r\n      carValue: \"\",\r\n      licensePlateUnitLength: 0,\r\n      commitCishu: 0,\r\n      dialogVisibleOne: false,\r\n      dialogVisibleTwe: false,\r\n      dialogVisibleThree: false,\r\n      dialogVisibleFore: false,\r\n      zaizhongNum: 5, //默认值是5  如果是6的情况下取消弹框\r\n      timer: null,\r\n      isRuning: false,\r\n      taigancarNumber: \"\",\r\n      daozhaValue: [],\r\n    };\r\n  },\r\n  created() {\r\n    // 5. 网络恢复后立刻重连\r\nwindow.addEventListener('online', () => {\r\n  this.connectMqttBroker();\r\n  this.connectMqttBroker2();\r\n  this.connectMqttBroker3();\r\n  console.info('网络恢复，重新连接 MQTT');\r\n  // client = mqtt.connect('wss://your.mqtt.broker:8084/mqtt', options);\r\n});\r\n    // 获取客户端一体机数据\r\n    this.getClient();\r\n    // this.getCarData()\r\n    // this.getToken_();\r\n\r\n    // setInterval(() => {\r\n    //   this.getToken_();\r\n    // }, 90 * 60000);\r\n    // console.log(this.encrypt('{ \"cardNumber\": \"京A12345\" }'), \"我是加密的数据\");\r\n    this.getDun();\r\n    if (\"serial\" in navigator) {\r\n      // console.log(\"浏览器支持Web Serial API\");\r\n    } else {\r\n      // console.log(\"浏览器不支持Web Serial API\");\r\n    }\r\n    // this.getToken_()\r\n    setInterval(() => {\r\n      // 300000\r\n      var timestamp = Date.now();\r\n      // console.log(timestamp, \"当前的时间\");\r\n      // console.log(timestamp - this.xintiaobao > 30000, \"我连接了mqtt\");\r\n      this.shebei1Status = timestamp - this.xintiaobao > 30000;\r\n      this.shebei2Status = timestamp - this.xintiaobao2 > 30000;\r\n    }, 30000);\r\n    // var that = this;\r\n    this.getmuhuSearch();\r\n  },\r\n  beforeDestroy() {\r\n    // 清理工作，例如停止播放，销毁播放器实例等\r\n    if (this.flvPlayer) {\r\n      this.flvPlayer.pause();\r\n      this.flvPlayer.unload();\r\n      this.flvPlayer.detachMediaElement();\r\n      this.flvPlayer.destroy();\r\n    }\r\n    if (this.flvPlayer2) {\r\n      this.flvPlayer2.pause();\r\n      this.flvPlayer2.unload();\r\n      this.flvPlayer2.detachMediaElement();\r\n      this.flvPlayer2.destroy();\r\n    }\r\n  },\r\n  watch: {\r\n    /**\r\n     * 监听车牌号变化的方法\r\n     *\r\n     * @param {string} newValue 新的车牌号\r\n     * @param {string} oldValue 旧的车牌号\r\n     *\r\n     * @description 当新的车牌号与旧的车牌号不同时，如果当前车辆状态为出场（即this.carStatus为false），\r\n     * 则通过axios向指定URL发送POST请求，将当前客户端ID和新的车牌号作为请求体发送。\r\n     * 请求成功后，打印返回的数据；请求失败时，打印错误信息。\r\n     * 同时，打印当前的车牌号信息，并取消订阅自动获取。\r\n     */\r\n    carNum(newValue, oldValue) {\r\n      if (newValue && this.carStatus == false) {\r\n        let res = {\r\n          clientId: this.ClientData.client.id,\r\n          stationId: this.ClientData.client.stationId,\r\n          carNumber: this.carNum,\r\n        };\r\n        // console.log(this.ClientData, \"我是在监听中拿的正常的\");\r\n        axios\r\n          .post(this.baseUrl + \"/car/checkWeight\", res)\r\n          .then((res) => {\r\n            console.log(res.data.data, \"我是在监听中拿的正常的\");\r\n            if (res.data.data == \"未申诉\") return;\r\n            this.$message.success(res.data.data);\r\n          })\r\n          .catch((err) => {\r\n            console.log(err, \"我是在监听中拿的错误的\");\r\n            this.$message.error(err);\r\n          });\r\n\r\n        console.log(\"newValue,我是拿到的车牌号\", newValue);\r\n\r\n        // 取消订阅自动获取\r\n      }\r\n    },\r\n    carStatus(newValue, oldValue) {\r\n      if (newValue) return;\r\n      let res = {\r\n        clientId: this.ClientData.client.id,\r\n        stationId: this.ClientData.client.stationId,\r\n        carNumber: this.carNum,\r\n      };\r\n      if (this.carNum) {\r\n        axios\r\n          .post(this.baseUrl + \"/car/checkWeight\", res)\r\n          .then((res) => {\r\n            console.log(res.data.data, \"我是在监听中拿的正常的\");\r\n            if (res.data.data == \"未申诉\") return;\r\n            this.$message.success(res.data.data);\r\n          })\r\n          .catch((err) => {\r\n            console.log(err, \"我是在监听中拿的错误的\");\r\n            this.$message.error(err);\r\n          });\r\n      }\r\n    },\r\n\r\n    licensePlateDoor(newValue, oldValue) {\r\n      this.getmuhuSearch(\"\");\r\n      if (newValue) {\r\n        this.updateState = true;\r\n        this.updateButton();\r\n        console.log(\"我现在是输入状态\");\r\n        // 取消订阅自动获取\r\n        this.client.unsubscribe(\r\n          \"device/\" + this.equimentId1 + \"/message/up/ivs_result\",\r\n          { qos: 0 },\r\n          (err, success) => {\r\n            if (!err) {\r\n              console.log(\"已经取消订阅自动获取数据接口\");\r\n            } else {\r\n              // console.log(\"取消订阅失败\");\r\n            }\r\n          }\r\n        );\r\n        this.client2.unsubscribe(\r\n          \"device/\" + this.equimentId2 + \"/message/up/ivs_result\",\r\n          { qos: 0 },\r\n          (err, success) => {\r\n            if (!err) {\r\n              // console.log(\"已经取消订阅自动获取数据接口\");\r\n            } else {\r\n              // console.log(\"取消订阅失败\");\r\n            }\r\n          }\r\n        );\r\n        this.client3.unsubscribe(\r\n          \"device/\" + this.equimentId3 + \"/message/up/ivs_result\",\r\n          { qos: 0 },\r\n          (err, success) => {\r\n            if (!err) {\r\n              // console.log(\"已经取消订阅自动获取数据接口\");\r\n            } else {\r\n              // console.log(\"取消订阅失败\");\r\n            }\r\n          }\r\n        );\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    firstSixItems() {\r\n      return this.searchResults.slice(0, 6);\r\n    },\r\n  },\r\n  methods: {\r\n    async handleEdit(id, row) {\r\n      try {\r\n        this.phoneLoading = true;\r\n        const res = await axios.get(`${this.baseUrl}/car/getImageById`, {\r\n          params: { id: row.id },\r\n        });\r\n\r\n        const { headImage, rearImage } = res.data.data;\r\n        this.urlimagL = headImage;\r\n        this.urlimagR = rearImage;\r\n        this.srcList = [headImage, rearImage];\r\n        this.centerDialogVisible = true;\r\n      } catch (error) {\r\n        console.error(\"获取图片失败:\", error);\r\n        this.$message.error(\"获取图片失败\");\r\n      } finally {\r\n        this.phoneLoading = false;\r\n      }\r\n    },\r\n    //  handleEdit(id, row) {\r\n    //       this.phoneLoading = true;\r\n\r\n    //       console.log(id, row);\r\n\r\n    //        axios\r\n    //         .get(this.baseUrl + \"/car/getImageById\", {\r\n    //           params: {\r\n    //             id: row.id,\r\n    //           },\r\n    //         })\r\n    //         .then((res) => {\r\n    //           console.log(res.data.data, \"我是图片的地址\");\r\n    //           this.urlimagL = res.data.data.headImage;\r\n    //           this.urlimagR = res.data.data.rearImage;\r\n\r\n    //           this.srcList.push(res.data.data.headImage);\r\n    //           this.srcList.push(res.data.data.rearImage);\r\n    //            this.centerDialogVisible = true;\r\n    //            this.phoneLoading = false;\r\n\r\n    //         });\r\n\r\n    //     },\r\n    // 过泵时间赋值\r\n    searchTime(val) {\r\n      if (val == null) {\r\n        this.formInline.startTime = null;\r\n        this.formInline.endTime = null;\r\n\r\n        return this.getHistory();\r\n      }\r\n      console.log(this.guobengTime, \"我是时间\");\r\n      this.formInline.startTime = this.guobengTime[0];\r\n      this.formInline.endTime = this.guobengTime[1];\r\n      this.pageData.pageNumber = 1;\r\n      this.getHistory();\r\n    },\r\n    handleChangeType(value) {\r\n      // this.carStatus = value\r\n      console.log(this.carStatus);\r\n      console.log(\"我点击了切换类型\");\r\n      // if (this.client && this.client2) {\r\n      //   this.replayget()\r\n      // }\r\n    },\r\n    fullscreenBig() {\r\n      document.documentElement.requestFullscreen();\r\n      document.documentElement.webkitRequestFullscreen();\r\n      document.documentElement.mozRequestFullScreen();\r\n      document.documentElement.msRequestFullscreen();\r\n    },\r\n    // 获取一体机客户端数据\r\n    getClient() {\r\n      console.log(this.baseUrl, \"99999999999999\");\r\n      axios\r\n        .get(this.baseUrl + \"/car/getProjectId\")\r\n        .then((res) => {\r\n          console.log(res, \"查看请求的信息!!!!666\");\r\n          this.ClientData = res.data;\r\n          this.equimentId1 = this.ClientData.cameras[0].sn;\r\n          this.equimentId2 = this.ClientData.cameras[1].sn;\r\n          // 第三个摄像头SN 用于道闸识别\r\n          if (this.ClientData.cameras.length > 2) {\r\n            this.equimentId3 = this.ClientData.cameras[2].sn;\r\n          }\r\n\r\n          // 拉取视频数据\r\n          this.getvideo();\r\n          this.getvideo2();\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误，例如记录日志或显示错误消息\r\n          console.error(\"请求失败:\", error);\r\n        });\r\n    },\r\n    // getCarData(){\r\n    //     axios.get(\"http://192.168.1.144:8077/car/getData\").then(res=>{\r\n    //       console.log(res,\"查看请求的信息!!!!666\")\r\n    //   })\r\n    // },\r\n    updateButton() {\r\n      this.submitLoading = false;\r\n      this.updateState = !this.updateState;\r\n      if (this.updateState) {\r\n        this.updateText = \"停止采集\";\r\n        this.buttonStatus = true;\r\n        this.flvPlayer.attachMediaElement(this.videoEl);\r\n        this.flvPlayer.unload();\r\n        // 监听错误事件\r\n        this.flvPlayer.on(flvjs.Events.ERROR, (err, errdet) => {\r\n          // 参数 err 是一级异常，errdet 是二级异常\r\n          if (err == flvjs.ErrorTypes.MEDIA_ERROR) {\r\n            console.log(\"媒体错误\");\r\n            if (errdet == flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {\r\n              console.log(\"媒体格式不支持\");\r\n            }\r\n          } else if (err == flvjs.ErrorTypes.NETWORK_ERROR) {\r\n            console.log(\"网络错误\");\r\n            if (errdet == flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {\r\n              console.log(\"http状态码异常\");\r\n            }\r\n          } else if (err == flvjs.ErrorTypes.OTHER_ERROR) {\r\n            console.log(\"其他异常：\", errdet);\r\n          }\r\n        });\r\n        this.flvPlayer.load();\r\n        this.flvPlayer.play();\r\n        this.connectMqttBroker();\r\n        this.connectMqttBroker2();\r\n        // 抬杆代码\r\n        if (this.ClientData.cameras.length > 2) {\r\n          this.connectMqttBroker3();\r\n        }\r\n\r\n        this.buttonStatus2 = true;\r\n        this.flvPlayer2.attachMediaElement(this.videoE2);\r\n        this.flvPlayer2.unload();\r\n        // 监听错误事件\r\n        this.flvPlayer2.on(flvjs.Events.ERROR, (err, errdet) => {\r\n          // 参数 err 是一级异常，errdet 是二级异常\r\n          if (err == flvjs.ErrorTypes.MEDIA_ERROR) {\r\n            console.log(\"媒体错误\");\r\n            if (errdet == flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {\r\n              console.log(\"媒体格式不支持\");\r\n            }\r\n          } else if (err == flvjs.ErrorTypes.NETWORK_ERROR) {\r\n            console.log(\"网络错误\");\r\n            if (errdet == flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {\r\n              console.log(\"http状态码异常\");\r\n            }\r\n          } else if (err == flvjs.ErrorTypes.OTHER_ERROR) {\r\n            console.log(\"其他异常：\", errdet);\r\n          }\r\n        });\r\n        this.flvPlayer2.load();\r\n        this.flvPlayer2.play();\r\n        // this.connectMqttBroker2();\r\n      } else {\r\n        this.updateText = \"开始采集\";\r\n        this.disconnectMqttClient();\r\n        this.disconnectMqttClient2();\r\n        // 抬杆代码\r\n        if (this.ClientData.cameras.length > 2) {\r\n          this.disconnectMqttClient3();\r\n        }\r\n      }\r\n    },\r\n    connectMqttBroker() {\r\n      if (this.client) {\r\n        this.client.end();\r\n      }\r\n      // console.log(\"===============\");\r\n      // console.log(this.initData.timestamp, this.initData.token, \"我连接了mqtt\")\r\n      // 连接到MQTT代理\r\n      this.client = mqtt.connect({\r\n        protocol: \"ws\",\r\n        hostname: this.mqttIp,\r\n        // hostname: \"*************\",\r\n        path: \"/mqtt\",\r\n        port: 8083,\r\n        username: \"admin\",\r\n        password: \"admin123\",\r\n        keepAliveInterval: 60,\r\n        mqttVersion: 5,\r\n        clientId: \"myclientid_\" + parseInt(Math.random() * 10000, 10),\r\n        // clientId: \"client_mqtt_\" + this.initData.clientId\r\n      });\r\n      this.client.on(\"connect\", () => {\r\n        console.log(\"Connected to MQTT broker\", \"我连接了mqtt1\");\r\n        // 订阅主题--重新抓取\r\n        this.client.subscribe(\r\n          \"device/\" + this.equimentId1 + \"/message/down/ivs_trigger/reply\",\r\n          { qos: 0 },\r\n          (err, granted) => {\r\n            console.log(\"Subscribed to topic999999\", granted);\r\n            if (!err) {\r\n              console.log(\"Subscribed to topic\", granted);\r\n            }\r\n          }\r\n        );\r\n        // 订阅车牌信息\r\n        // this.client.subscribe('/carNumbers/' + this.initData.projectId, { qos: 1 }, (err, granted) => {\r\n        //   console.log('订阅车牌信息成功', granted);\r\n        // });\r\n        // 订阅主题--订阅心跳\r\n        this.client.subscribe(\r\n          \"device/\" + this.equimentId1 + \"/message/up/keep_alive\",\r\n          { qos: 0 },\r\n          (err, granted) => {\r\n            // console.log(\"订阅心跳成功1\", granted);\r\n            if (!err) {\r\n              console.log(\"Subscribed to topic\", granted);\r\n            }\r\n          }\r\n        );\r\n        // 订阅主题--抬杆\r\n        this.client.subscribe(\r\n          \"device/\" + this.equimentId1 + \"/message/down/gpio_out/reply\",\r\n          { qos: 0 },\r\n          (err, granted) => {\r\n            // console.log(\"抬杆订阅成功\", granted);\r\n            if (!err) {\r\n              console.log(\"Subscribed to topic\", granted);\r\n            }\r\n          }\r\n        );\r\n        // 订阅主题----获取结果\r\n        this.client.subscribe(\r\n          \"device/\" + this.equimentId1 + \"/message/up/ivs_result\",\r\n          { qos: 0 },\r\n          (err, success) => {\r\n            if (!err) {\r\n              console.log(\"Subscribed to my/topic\");\r\n            }\r\n          }\r\n        );\r\n      });\r\n      this.client.on(\"message\", (topic, message) => {\r\n        if (topic == \"device/\" + this.equimentId1 + \"/message/up/ivs_result\") {\r\n          const payload = JSON.parse(message.toString());\r\n          console.log(payload, \"我是获取的数据1\");\r\n          this.leftUrl =\r\n            \"data:image/png;base64,\" +\r\n            payload.payload.AlarmInfoPlate.result.PlateResult\r\n              .full_image_content;\r\n          this.triggerType =\r\n            payload.payload.AlarmInfoPlate.result.PlateResult.triggerType;\r\n          if (this.carStatus) {\r\n            console.log(\"入场数据===================\");\r\n            console.log(payload, \"查看获取到的入场数据\");\r\n            this.payload = JSON.parse(message.toString());\r\n            if (\r\n              payload.payload.AlarmInfoPlate.result.PlateResult.is_fake_plate ==\r\n              1\r\n            ) {\r\n              this.showCar = this.base64Decode(\r\n                payload.payload.AlarmInfoPlate.result.PlateResult.license\r\n              );\r\n              this.dialogCarVisible = true;\r\n            } else {\r\n              let getData =\r\n                payload.payload.AlarmInfoPlate.result.PlateResult.license;\r\n              if (getData == \"X+aXoF8=\") {\r\n                this.carNum = \"\";\r\n                this.licensePlateUnit = [];\r\n                return this.$message({\r\n                  message: \"未识别到车牌\",\r\n                  type: \"warning\",\r\n                });\r\n              } else {\r\n                var carName = this.base64Decode(getData);\r\n                this.testCarNum(carName, \"自动获取\");\r\n                console.log(this.carNum, \"我是车牌号\");\r\n              }\r\n            }\r\n          }\r\n          // this.payload = JSON.parse(message.toString());\r\n\r\n          console.log(payload, \"我是获取的数据1\");\r\n        }\r\n        if (\r\n          topic ==\r\n          \"device/\" + this.equimentId1 + \"/message/down/ivs_trigger/reply\"\r\n        ) {\r\n          const zdImg = JSON.parse(message.toString());\r\n          console.log(zdImg, \"我是请求的数据\");\r\n        }\r\n        // 控制抬杆\r\n        if (\r\n          topic ==\r\n          \"device/\" + this.equimentId1 + \"/message/down/gpio_out/reply\"\r\n        ) {\r\n          const zdImg = JSON.parse(message.toString());\r\n          // console.log(zdImg, \"我是抬杆\");\r\n        }\r\n        if (topic == \"device/\" + this.equimentId1 + \"/message/up/keep_alive\") {\r\n          const zdImg = JSON.parse(message.toString());\r\n          // console.log(zdImg, \"我是心跳1\");\r\n          this.xintiaobao = zdImg.timestamp * 1000;\r\n          // var date=Date.now()\r\n          // console.log(time.toLocaleString(), \"我是当前时间\")\r\n          // console.log(this.xintiaobao, \"我是心跳时间戳\")\r\n        }\r\n        // message是Buffer格式\r\n        console.log(\r\n          `Received message on topic ${topic}: ${message.toString()}`\r\n        );\r\n      });\r\n      this.client.on(\"error\", (err) => {\r\n        console.error(\"MQTT error:\", err);\r\n      });\r\n    },\r\n    connectMqttBroker2() {\r\n      if (this.client2) {\r\n        this.client2.end();\r\n      }\r\n      // console.log(\"===============\");\r\n      // 连接到MQTT代理\r\n      this.client2 = mqtt.connect({\r\n        protocol: \"ws\",\r\n        hostname: this.mqttIp,\r\n        // hostname: \"*************\",\r\n        path: \"/mqtt\",\r\n        port: 8083,\r\n        username: \"admin\",\r\n        password: \"admin123\",\r\n        keepAliveInterval: 60,\r\n        mqttVersion: 5,\r\n        clientId: \"myclientid_2\" + parseInt(Math.random() * 10000, 10),\r\n      });\r\n      this.client2.on(\"connect\", () => {\r\n        console.log(\"Connected to MQTT broker\", \"我连接了mqtt2\");\r\n        // 订阅主题--订阅心跳\r\n        this.client2.subscribe(\r\n          \"device/\" + this.equimentId2 + \"/message/up/keep_alive\",\r\n          { qos: 0 },\r\n          (err, granted) => {\r\n            console.log(\"订阅心跳成功2\", granted);\r\n            if (!err) {\r\n              console.log(\"Subscribed to topic\", granted);\r\n            }\r\n          }\r\n        );\r\n        // 订阅主题----获取结果\r\n        this.client2.subscribe(\r\n          \"device/\" + this.equimentId2 + \"/message/up/ivs_result\",\r\n          { qos: 0 },\r\n          (err, success) => {\r\n            console.log(success, \"我是订阅成功2\");\r\n            if (!err) {\r\n              console.log(\"Subscribed to my/topic2\");\r\n            }\r\n          }\r\n        );\r\n      });\r\n      this.client2.on(\"message\", (topic, message) => {\r\n        if (topic == \"device/\" + this.equimentId2 + \"/message/up/ivs_result\") {\r\n          const payload = JSON.parse(message.toString());\r\n          this.rightUrl =\r\n            \"data:image/png;base64,\" +\r\n            payload.payload.AlarmInfoPlate.result.PlateResult\r\n              .full_image_content;\r\n          if (!this.carStatus) {\r\n            console.log(\"出场数据=================================\");\r\n            console.log(payload, \"查看获取到的出场数据\");\r\n            this.payload = JSON.parse(message.toString());\r\n            // 判断车牌真伪 0真 1伪\r\n            if (\r\n              payload.payload.AlarmInfoPlate.result.PlateResult.is_fake_plate ==\r\n              1\r\n            ) {\r\n              this.showCar = this.base64Decode(\r\n                payload.payload.AlarmInfoPlate.result.PlateResult.license\r\n              );\r\n              this.dialogCarVisible = true;\r\n            } else {\r\n              let getData =\r\n                payload.payload.AlarmInfoPlate.result.PlateResult.license;\r\n              if (getData == \"X+aXoF8=\") {\r\n                this.carNum = \"\";\r\n                this.licensePlateUnit = [];\r\n                return this.$message({\r\n                  message: \"未识别到车牌\",\r\n                  type: \"warning\",\r\n                });\r\n              } else {\r\n                var carName = this.base64Decode(getData);\r\n                // this.carNum = carName;\r\n                this.testCarNum(carName, \"自动获取\");\r\n                console.log(this.carNum, \"我是车牌号\");\r\n              }\r\n            }\r\n          }\r\n\r\n          console.log(payload, \"我是获取的数据2\");\r\n          // 绑定图片路径\r\n          console.log(\r\n            payload.payload.AlarmInfoPlate.result.PlateResult\r\n              .full_image_content,\r\n            \"查看图片路径\"\r\n          );\r\n\r\n          this.triggerType =\r\n            payload.payload.AlarmInfoPlate.result.PlateResult.triggerType;\r\n        }\r\n        if (\r\n          topic ==\r\n          \"device/\" + this.equimentId2 + \"/message/down/ivs_trigger/reply\"\r\n        ) {\r\n          const zdImg = JSON.parse(message.toString());\r\n          console.log(zdImg, \"我是请求的数据\");\r\n        }\r\n        if (topic == \"device/\" + this.equimentId2 + \"/message/up/keep_alive\") {\r\n          const zdImg = JSON.parse(message.toString());\r\n          console.log(zdImg, \"我是心跳2\");\r\n          this.xintiaobao2 = zdImg.timestamp * 1000;\r\n          // var date=Date.now()\r\n          // console.log(time.toLocaleString(), \"我是当前时间\")\r\n          // console.log(this.xintiaobao, \"我是心跳时间戳\")\r\n        }\r\n        // message是Buffer格式\r\n        console.log(\r\n          `Received message on topic ${topic}: ${message.toString()}`\r\n        );\r\n      });\r\n      this.client2.on(\"error\", (err) => {\r\n        console.error(\"MQTT error:\", err);\r\n      });\r\n      // this.client.publish('device/13a5ed3a-d6ce95c8/message/down/ivs_trigger', 'Hello, MQTT!');\r\n    },\r\n    /**\r\n     * 判断车牌号是否已经存在数组中，并根据情况处理\r\n     *\r\n     * @param {string} data - 车牌号\r\n     */\r\n    // 抬杆代码。先注释掉，暂时不用(开始)\r\n\r\n    daozha_testCarNum(data) {\r\n      // 判断daozhaValue数组中是否包含data元素\r\n      // this.carNum = data;\r\n      var value = this.daozhaValue.includes(data);\r\n      console.log(value, \"我是value请求的值\");\r\n      // 将data元素添加到daizhaValue数组的末尾\r\n      if (!value) {\r\n        this.daozhaValue.push(data);\r\n        this.handle_Taigan(data);\r\n\r\n        setTimeout(() => {\r\n          var remove = data;\r\n          this.daozhaValue = this.daozhaValue.filter((item) => {\r\n            return item !== remove;\r\n          });\r\n        }, 100000);\r\n      } else {\r\n        console.log(\"车牌号 \" + data + \"已抬杆，请稍后再试！\");\r\n        this.$message({\r\n          type: \"warning\",\r\n          message: \"车牌号 \" + data + \"已抬杆，请稍后再试！\",\r\n        });\r\n      }\r\n    },\r\n    // 结束\r\n    /**\r\n     *\r\n     * 连接到MQTT代理的方法\r\n     */\r\n    // 抬杆代码。先注释掉，暂时不用(开始)\r\n    connectMqttBroker3() {\r\n      if (this.client3) {\r\n        this.client3.end();\r\n      }\r\n      // console.log(\"===============\");\r\n      // 连接到MQTT代理\r\n\r\n      this.client3 = mqtt.connect({\r\n        protocol: \"ws\",\r\n        hostname: this.mqttIp,\r\n        // hostname: \"*************\",\r\n        path: \"/mqtt\",\r\n        port: 8083,\r\n        username: \"admin\",\r\n        password: \"admin123\",\r\n        keepAliveInterval: 60,\r\n        mqttVersion: 5,\r\n        // clientId: \"myclientid_3\" + parseInt(Math.random() * 10000, 10),\r\n        clientId: \"myclientid_3\" + parseInt(Math.random() * 10000, 10),\r\n      });\r\n      this.client3.on(\"connect\", () => {\r\n        console.log(\"Connected to MQTT broker\", \"我连接了mqtt2\");\r\n        // 订阅主题--订阅心跳\r\n        this.client3.subscribe(\r\n          \"device/\" + this.equimentId3 + \"/message/up/keep_alive\",\r\n          { qos: 0 },\r\n          (err, granted) => {\r\n            console.log(\"订阅心跳成功2\", granted);\r\n            if (!err) {\r\n              console.log(\"Subscribed to topic\", granted);\r\n            }\r\n          }\r\n        );\r\n        // 订阅主题----获取结果\r\n        this.client3.subscribe(\r\n          \"device/\" + this.equimentId3 + \"/message/up/ivs_result\",\r\n          { qos: 0 },\r\n          (err, success) => {\r\n            console.log(success, \"我是订阅成功2\");\r\n            if (!err) {\r\n              console.log(\"Subscribed to my/topic2\");\r\n            }\r\n          }\r\n        );\r\n\r\n        // 订阅主题--抬杆\r\n        this.client3.subscribe(\r\n          \"device/\" + this.equimentId3 + \"/message/down/gpio_out/reply\",\r\n          { qos: 0 },\r\n          (err, granted) => {\r\n            console.log(\"抬杆订阅成功\", granted);\r\n\r\n            if (!err) {\r\n              console.log(\"Subscribed to topic\", granted);\r\n            }\r\n          }\r\n        );\r\n      });\r\n      this.client3.on(\"message\", (topic, message) => {\r\n        if (topic == \"device/\" + this.equimentId3 + \"/message/up/ivs_result\") {\r\n          const payload = JSON.parse(message.toString());\r\n          console.log(payload, \"我是获取的第三个摄像头数据3\");\r\n          let getData =\r\n            payload.payload.AlarmInfoPlate.result.PlateResult.license;\r\n          console.log(getData, \"查看一下我是第三个摄像头的数据的getData\");\r\n          if (getData == \"X+aXoF8=\") {\r\n            return this.$message({\r\n              message: \"闸机未识别到车牌，请点击左上角第二个按钮触发重新识别\",\r\n              type: \"warning\",\r\n            });\r\n          } else {\r\n            var carName = this.base64Decode(getData);\r\n            console.log(JSON.stringify({ carNumber: carName }), \"我是提交的\");\r\n            // 加密车牌号----AES\r\n            var data = this.encrypt(JSON.stringify({ carNumber: carName }));\r\n            console.log(this.requestToken, \"发送请求的token\");\r\n\r\n            axios\r\n              .get(this.baseUrl + \"/car/getCarNumById\", data, {\r\n                headers: {\r\n                  \"Content-Type\": \"application/json; charset=UTF-8\",\r\n                  token: this.requestToken,\r\n                },\r\n              })\r\n              .then((res) => {\r\n                console.log(res, \"我是校验的车牌号是否存在\");\r\n                let datas = res.data;\r\n                let cartype = datas.some((item) => item === carName);\r\n                // 如果车牌号存在cartype=true\r\n                console.log(cartype, \"确认\");\r\n                if (cartype) {\r\n                  this.daozha_testCarNum(carName);\r\n                } else {\r\n                  this.$message({\r\n                    type: \"error\",\r\n                    message: \"车牌号\" + carName + \"未提交运砂申请，抬杆失败！\",\r\n                  });\r\n                  this.carNum = \"\";\r\n                  this.licensePlateUnit = [];\r\n                }\r\n              })\r\n              .catch((err) => {\r\n                console.log(err, \"我是错误\");\r\n              });\r\n            console.log(carName, \"我是第三个摄像头识别到的车牌号\");\r\n          }\r\n\r\n          console.log(payload, \"我是获取的数据3\");\r\n        }\r\n        if (\r\n          topic ==\r\n          \"device/\" + this.equimentId3 + \"/message/down/ivs_trigger/reply\"\r\n        ) {\r\n          const zdImg = JSON.parse(message.toString());\r\n          console.log(zdImg, \"我是请求的数据\");\r\n        }\r\n        if (topic == \"device/\" + this.equimentId3 + \"/message/up/keep_alive\") {\r\n          const zdImg = JSON.parse(message.toString());\r\n          console.log(zdImg, \"我是心跳2\");\r\n          this.xintiaobao2 = zdImg.timestamp * 1000;\r\n        }\r\n        // 获取抬杆回复\r\n\r\n        if (\r\n          topic ==\r\n          \"device/\" + this.equimentId3 + \"/message/down/gpio_out/reply\"\r\n        ) {\r\n          const tgImg = JSON.parse(message.toString());\r\n          console.log(tgImg, \"我是抬杆回复的数据\");\r\n          if (tgImg.id) {\r\n            this.tgId = tgImg.id.slice(0, 12);\r\n            this.tgid = \"myclientid_3\";\r\n            console.log(this.tgId, \"我是抬杆的id\");\r\n            console.log(this.tgid, \"我是mqtt的id\");\r\n          }\r\n\r\n          if (tgImg.code == 200 && this.tgid == this.tgId) {\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"抬杆成功！请尽快通过！\",\r\n            });\r\n            var liftRodDataVo = {\r\n              clientId: this.ClientData.client.id,\r\n              type: \"in\",\r\n              carNumber: this.taigancarNumber,\r\n              takeOff: true,\r\n            };\r\n\r\n            axios\r\n              .post(this.baseUrl + \"/car/submitLiftRodData\", liftRodDataVo)\r\n              .then((res) => {\r\n                console.log(res, \"我是数据\");\r\n              })\r\n              .catch((err) => {\r\n                console.log(err, \"我是错误\");\r\n              });\r\n          } else {\r\n            console.log(tgImg, \"我是抬杆错误的数据\");\r\n            if (  this.tgid == this.tgId) {\r\n   this.$message({\r\n              type: \"error\",\r\n              message: \"抬杆失败！\",\r\n            });\r\n            }\r\n         \r\n          }\r\n        }\r\n        // message是Buffer格式\r\n        console.log(\r\n          `Received message on topic ${topic}: ${message.toString()}`\r\n        );\r\n      });\r\n      this.client3.on(\"error\", (err) => {\r\n        console.error(\"MQTT error:\", err);\r\n      });\r\n    },\r\n    // 结束\r\n    /**\r\n     * 确认车牌号是否合法并触发相应操作\r\n     */\r\n    /**\r\n     * 确认车辆信息并处理相关逻辑\r\n     */\r\n    confirm() {\r\n      console.log(this.carIs, \"电器\");\r\n      if (this.licensePlateUnit.length >= 7) {\r\n        console.log(\"车牌是：\" + this.licensePlateUnit.join(\"\"));\r\n        this.carNum = this.licensePlateUnit.join(\"\");\r\n        console.log();\r\n        if (!this.carStatus) {\r\n          this.testCarNum(this.carNum, \"确认\");\r\n        }\r\n        // 如果载重大于5吨，判断车辆已上泵，不走抬杆逻辑\r\n        else if (this.weight > 5) {\r\n          this.testCarNum(this.carNum, \"确认\");\r\n        } else {\r\n          var data = this.encrypt(JSON.stringify({ carNumber: this.carNum }));\r\n          axios\r\n            .get(this.baseUrl + \"/car/getCarNumById\", data, {\r\n              headers: {\r\n                \"Content-Type\": \"application/json; charset=UTF-8\",\r\n                token: this.requestToken,\r\n              },\r\n            })\r\n            .then((res) => {\r\n              console.log(res, \"我是校验的车牌号是否存在\");\r\n              let datas = res.data;\r\n              let cartype = datas.some((item) => item === this.carNum);\r\n              // 如果车牌号存在cartype=true\r\n              console.log(cartype, \"确认\");\r\n              if (cartype) {\r\n                // 抬杆代码，先注释掉，暂时用这个方法触发抬杆\r\n                // carStatus为true时，表示车辆此时是入场，\r\n\r\n                if (this.carStatus) {\r\n                  this.daozha_testCarNum(this.carNum);\r\n                }\r\n                this.licensePlateDoor = false;\r\n              } else {\r\n                this.$message({\r\n                  type: \"error\",\r\n                  message: \"车牌号未提交运砂申请，抬杆失败！\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {\r\n              console.log(err, \"我是错误\");\r\n            });\r\n        }\r\n      }\r\n    },\r\n    // 强制刷新页面\r\n    SX() {\r\n      window.location.reload();\r\n    },\r\n    // 抬杆代码，先注释掉，暂时用这个方法触发抬杆\r\n\r\n    /**\r\n     * 触发抬杆事件\r\n     */\r\n    TG() {\r\n      if (this.client3) {\r\n        this.disconnectMqttClient3();\r\n      }\r\n      this.connectMqttBroker3();\r\n      console.log(\"触发了我一次抬杆\");\r\n      // 第三个摄像头\r\n      var params3 = {\r\n        id: \"camera3\",\r\n        sn: this.equimentId3,\r\n        name: \"ivs_trigger\",\r\n        version: \"1.0\",\r\n        timestamp: **********,\r\n        payload: {\r\n          type: \"ivs_trigger\",\r\n          body: {},\r\n        },\r\n      };\r\n      console.log(\"重新获取\");\r\n      // 发布手动触发抓拍\r\n      this.client3.publish(\r\n        \"device/\" + this.equimentId3 + \"/message/down/ivs_trigger\",\r\n        JSON.stringify(params3)\r\n      );\r\n    },\r\n    // 结束\r\n    // 抬杆代码，用这个方法触发抬杆\r\n    handle_Taigan(taigancarNumber) {\r\n      this.taigancarNumber = taigancarNumber;\r\n      this.connectMqttBroker3();\r\n      // 设置id值为随机值\r\n      const id = \"myclientid_3\" + parseInt(Math.random() * 10000, 10);\r\n      this.cremarsId.push(id);\r\n      console.log(\"触发抬杆\");\r\n      var params = {\r\n        id: id, // 消息 ID，用于关联具体消息\r\n        sn: this.equimentId3, // 设备序列号\r\n        name: \"gpio_out\", // 消息名称\r\n        version: \"1.0\", // 消息版本，目前都填 1.0\r\n        timestamp: **********, // 时间戳\r\n        payload: {\r\n          type: \"gpio_out\",\r\n          body: {\r\n            delay: 500,\r\n            io: 0,\r\n            value: 2,\r\n          },\r\n        },\r\n      };\r\n      this.client3.publish(\r\n        \"device/\" + this.equimentId3 + \"/message/down/gpio_out\",\r\n        JSON.stringify(params)\r\n      );\r\n    },\r\n    // 结束\r\n    searchCardNum() {\r\n      var params = {\r\n        carNumber: this.formInline.cardNumber,\r\n        startTime: this.formInline.startTime,\r\n        endTime: this.formInline.endTime,\r\n        pageNumber: 1,\r\n        pageSize: 10,\r\n      };\r\n\r\n      axios\r\n        .get(this.baseUrl + \"/car/getDataByCarNumber\", { params })\r\n        .then((response) => {\r\n          console.log(response.data, \"历史数据\");\r\n          console.log(params, \"我是查询参数\");\r\n          this.gridData = response.data.data;\r\n\r\n          this.pageData.total = response.data.total;\r\n          this.pageData.pageNumber = 1;\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误\r\n          console.error(error);\r\n        });\r\n    },\r\n    getHistory(num) {\r\n      this.loading = true;\r\n      if (num == 1) {\r\n        this.pageData.pageNumber = 1;\r\n      }\r\n\r\n      var params = {\r\n        carNumber: this.formInline.cardNumber,\r\n        startTime: this.formInline.startTime,\r\n        endTime: this.formInline.endTime,\r\n        pageNumber: this.pageData.pageNumber,\r\n        pageSize: this.pageData.pageSize,\r\n      };\r\n      console.log(params, \"我是查询参数\");\r\n      axios\r\n        .get(this.baseUrl + \"/car/getDataByCarNumber\", { params })\r\n        // this.pageData.pageSize\r\n        .then((response) => {\r\n          console.log(response.data, \"历史数据\");\r\n          this.gridData = response.data.data;\r\n          this.pageData.total = response.data.total;\r\n          this.loading = false;\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误\r\n          console.error(error);\r\n        });\r\n    },\r\n    handleSizeChange(val) {\r\n      this.pageData.pageSize = val;\r\n      this.pageData.pageNumber = 1;\r\n      this.getHistory();\r\n      console.log(`每页 ${val} 条`);\r\n    },\r\n    handleCurrentChange(val) {\r\n      this.pageData.pageNumber = val;\r\n      this.getHistory();\r\n      console.log(`当前页: ${val}`);\r\n    },\r\n\r\n    // 获取泵站名称\r\n    getProjectInfo() {\r\n      var data = {};\r\n      axios\r\n        .post(process.env.VUE_APP_BASE_URL + \"/openapi/station/list\", data, {\r\n          headers: {\r\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n            token: this.requestToken,\r\n          },\r\n        })\r\n        .then((res) => {\r\n          console.log(res, \"我是项目信息\");\r\n          this.weightInfo = res.data.list;\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误，例如记录日志或显示错误消息\r\n          console.error(\"请求失败:\", error);\r\n        });\r\n    },\r\n\r\n    // 校验车牌号\r\n    testCarNum(carNum, buttoTtype) {\r\n      console.log(JSON.stringify({ carNumber: carNum }), \"我是提交的\");\r\n      // 加密车牌号----AES\r\n      var data = this.encrypt(JSON.stringify({ carNumber: carNum }));\r\n      console.log(this.requestToken, \"发送请求的token\");\r\n      axios\r\n        .get(this.baseUrl + \"/car/getCarNumById\", data, {\r\n          headers: {\r\n            \"Content-Type\": \"application/json; charset=UTF-8\",\r\n            token: this.requestToken,\r\n          },\r\n        })\r\n        .then((res) => {\r\n          console.log(res, \"我是校验的车牌号是否存在\");\r\n          let datas = res.data;\r\n          let cartype = datas.some((item) => item === carNum);\r\n          // 如果车牌号存在cartype=true\r\n          console.log(cartype, \"确认\");\r\n          this.carIs = cartype;\r\n          console.log(this.carIs, \"我是校验的车牌号是否存在\");\r\n          if (cartype && buttoTtype == \"确认\") {\r\n            console.log(\"校验成功\");\r\n            this.licensePlateDoor = false;\r\n            this.submitLoading = false;\r\n          } else if (cartype && buttoTtype == \"提交\") {\r\n            // console.log(this.encrypt(jsonString), \"我是解密的数据\")\r\n\r\n            var match = this.leftUrl.match(\r\n              /data:image\\/(png|jpg|jpeg);base64,(.*)/\r\n            );\r\n            var match2 = this.rightUrl.match(\r\n              /data:image\\/(png|jpg|jpeg);base64,(.*)/\r\n            );\r\n            // console.log(match, match2, \"55555555555555555555555555\");\r\n            if (match) {\r\n              var matchBase64Left = match[2];\r\n            }\r\n            if (match2) {\r\n              var matchBase64Left2 = match2[2];\r\n            }\r\n            // this.weight=38;\r\n            var postData = {\r\n              // 泵站i\r\n              stationId: this.ClientData.client.stationId,\r\n              // 车牌号\r\n              carNumber: this.carNum,\r\n              // 重量\r\n              weight: this.weight,\r\n              // weight:30,\r\n              dataType: this.carStatus ? \"in\" : \"out\",\r\n              fileList: [matchBase64Left, matchBase64Left2],\r\n            };\r\n            // 提交图片\r\n            var weighingDataVo = {\r\n              weight: this.weight,\r\n              // weight: 80,\r\n              carNumber: this.carNum,\r\n              headImage: matchBase64Left,\r\n              rearImage: matchBase64Left2,\r\n              dataType: this.carStatus ? \"in\" : \"out\",\r\n            };\r\n            // 车辆重量大于45且是进场数据提交\r\n            if (this.weight < 1) {\r\n              this.$message({\r\n                message: \"提交重量不能小于1吨\",\r\n                type: \"warning\",\r\n              });\r\n              this.submitLoading = false;\r\n              return;\r\n            }\r\n            // 如果是第一次点击提交按钮 提交重量大于45吨 且是进场数据 弹出提示框\r\n            if (\r\n              this.commitCishu == 0 &&\r\n              this.weight > 30 &&\r\n              this.carStatus == true\r\n            ) {\r\n              this.dialogVisibleOne = true;\r\n              this.commitCishu++;\r\n              this.submitLoading = false;\r\n              return;\r\n            }\r\n            // 如果是第二次点击提交按钮 提交重量大于45吨 且是进场数据 弹出提示框\r\n            if (\r\n              this.commitCishu == 1 &&\r\n              this.weight > 30 &&\r\n              this.carStatus == true\r\n            ) {\r\n              this.dialogVisibleTwe = true;\r\n              this.commitCishu++;\r\n              this.submitLoading = false;\r\n              return;\r\n            }\r\n\r\n            // 如果是第一次点击提交按钮 提交重量小于等于45吨 且是出场数据 弹出提示框\r\n            if (\r\n              this.commitCishu == 0 &&\r\n              this.weight <= 30 &&\r\n              this.carStatus == false\r\n            ) {\r\n              this.dialogVisibleThree = true;\r\n              this.commitCishu++;\r\n              this.submitLoading = false;\r\n              return;\r\n            }\r\n            // 如果是第二次点击提交按钮 提交重量小于等于45吨 且是出场数据 弹出提示框\r\n            if (\r\n              this.commitCishu == 1 &&\r\n              this.weight <= 30 &&\r\n              this.carStatus == false\r\n            ) {\r\n              this.dialogVisibleFore = true;\r\n              this.commitCishu++;\r\n              this.submitLoading = false;\r\n              return;\r\n            }\r\n\r\n            axios\r\n              .post(this.baseUrl + \"/car/submitData\", weighingDataVo, {\r\n                headers: {\r\n                  \"Content-Type\": \"application/json; charset=UTF-8\",\r\n                },\r\n              })\r\n              .then((response) => {\r\n                if (response.data.code == 200) {\r\n                  this.submitLoading = false;\r\n                  this.commitCishu = 0;\r\n                  this.$message({\r\n                    message: \"提交成功\",\r\n                    type: \"success\",\r\n                  });\r\n                  console.log(\"数据提交成功\");\r\n                  console.log(response.data, \"提交\");\r\n                  this.leftUrl = \"\";\r\n                  this.rightUrl = \"\";\r\n                  this.weight = \"\";\r\n                  this.licensePlateUnit = [];\r\n                  // 恢复成为点击状态\r\n                  // 订阅获取消息通知\r\n                  if (this.client) {\r\n                    this.updateState = false;\r\n                    this.updateButton();\r\n                    this.client.subscribe(\r\n                      \"device/\" + this.equimentId1 + \"/message/up/ivs_result\",\r\n                      { qos: 0 },\r\n                      (err, success) => {\r\n                        if (!err) {\r\n                          console.log(\"已成功订阅获取信息\");\r\n                        } else {\r\n                          console.log(\"重新订阅获取信息\");\r\n                        }\r\n                      }\r\n                    );\r\n                    this.client2.subscribe(\r\n                      \"device/\" + this.equimentId2 + \"/message/up/ivs_result\",\r\n                      { qos: 0 },\r\n                      (err, success) => {\r\n                        if (!err) {\r\n                          console.log(\"已成功订阅获取信息\");\r\n                        } else {\r\n                          console.log(\"重新订阅获取信息\");\r\n                        }\r\n                      }\r\n                    );\r\n                    this.client3.subscribe(\r\n                      \"device/\" + this.equimentId3 + \"/message/up/ivs_result\",\r\n                      { qos: 0 },\r\n                      (err, success) => {\r\n                        if (!err) {\r\n                          console.log(\"已成功订阅获取信息\");\r\n                        } else {\r\n                          console.log(\"重新订阅获取信息\");\r\n                        }\r\n                      }\r\n                    );\r\n                  }\r\n                } else {\r\n                  this.submitLoading = false;\r\n                  this.commitCishu = 0;\r\n                  this.$message({\r\n                    message: response.data.message,\r\n                    type: \"warning\",\r\n                  });\r\n                }\r\n\r\n                // this.weight = \"\";\r\n              })\r\n              .catch((error) => {\r\n                console.error(\"请求失败:\", error);\r\n              });\r\n          } else if (this.carIs && buttoTtype == \"自动获取\") {\r\n            this.carNum = carNum;\r\n            // this.licensePlateUnit = this.carNum.split(\"\");\r\n          } else {\r\n            if (buttoTtype == \"自动获取\") {\r\n              this.dialogCarIsVisible = true;\r\n              // this.$message({\r\n              //   message: carName +'此车牌未录入',\r\n              //   type: 'warning',\r\n              // });\r\n              // 如果车主未提交运砂申请,置空车牌号信息\r\n              this.carNum = \"\";\r\n              this.licensePlateUnit = [];\r\n              return;\r\n            }\r\n            this.$message({\r\n              message: \"车主未提交运砂申请,禁止通过!\",\r\n              type: \"warning\",\r\n            });\r\n            // this.submitLoading = false;\r\n          }\r\n          console.log(this.carIs, \"66666\");\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误，例如记录日志或显示错误消息\r\n          console.error(\"请求失败:\", error);\r\n        });\r\n    },\r\n    //  获取模糊搜索车牌列表\r\n    getmuhuSearch(value) {\r\n      this.carValue = value;\r\n      console.log(value, \"我是value\");\r\n      var params = {\r\n        numberStr: value,\r\n      };\r\n      console.log(params, \"我是参数\");\r\n      axios\r\n        .get(this.baseUrl + \"/car/getCarNumById\", { params })\r\n        .then((response) => {\r\n          this.searchResults = [];\r\n          if (response.data.length > 0) {\r\n            this.searchResults = response.data;\r\n            // 处理响应数据\r\n\r\n            console.log(response.data);\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误\r\n          console.error(error);\r\n        });\r\n    },\r\n\r\n    submitData() {\r\n      this.submitLoading = true;\r\n      console.log(\"提交\");\r\n      console.log(this.carNum, \"我是提交的车牌号\");\r\n      if (this.carNum == \"\" || this.carNum == undefined) {\r\n        this.$message({\r\n          message: \"请输入车牌号\",\r\n          type: \"warning\",\r\n        });\r\n\r\n        return;\r\n      } else {\r\n        // 校验车牌号是否已经存在------车主已提交运砂申请\r\n        this.testCarNum(this.carNum, \"提交\");\r\n        console.log(this.carIs, \"我是carIs66\");\r\n      }\r\n    },\r\n    // 通过AES加密数据\r\n    encrypt(text) {\r\n      const encrypted = CryptoJS.AES.encrypt(text, key, {\r\n        // iv: iv,\r\n        mode: CryptoJS.mode.ECB,\r\n        padding: CryptoJS.pad.Pkcs7,\r\n      });\r\n      return encrypted.toString();\r\n    },\r\n\r\n    CarisFalse() {\r\n      this.dialogCarIsVisible = false;\r\n    },\r\n\r\n    // 获取请求接口的token\r\n    // 每个项目都要自己配置token\r\n    getToken_() {\r\n      var data = JSON.stringify({\r\n        appId: \"44\",\r\n        secret: \"22\",\r\n      });\r\n\r\n      axios\r\n        .post(process.env.VUE_APP_BASE_URL + \"/openapi/token/getToken\", data, {\r\n          headers: {\r\n            \"Content-Type\": \"application/json; charset=UTF-8\",\r\n          },\r\n        })\r\n        .then((res) => {\r\n          console.log(res, \"我是token设备的\");\r\n          this.requestToken = res.data.token;\r\n          // this.getProjectInfo();\r\n          console.log(this.requestToken, \"获取token\");\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误，例如记录日志或显示错误消息\r\n          console.error(\"请求失败:\", error);\r\n        });\r\n    },\r\n\r\n    handleClose() {\r\n      this.dialogCarVisible = false;\r\n    },\r\n    handleCloseOne() {\r\n      this.dialogVisibleOne = false;\r\n    },\r\n    handleCloseTwe() {\r\n      this.dialogVisibleTwe = false;\r\n    },\r\n    handleCloseThree() {\r\n      this.dialogVisibleThree = false;\r\n    },\r\n    handleCloseFore() {\r\n      this.dialogVisibleFore = false;\r\n    },\r\n    carTrue(value) {\r\n      if (value == \"获取\") {\r\n        this.dialogCarVisible = false;\r\n        let getData =\r\n          this.payload.payload.AlarmInfoPlate.result.PlateResult.license;\r\n        // 车牌号获取失败的返回值是 X+aXoF8=\r\n        if (getData == \"X+aXoF8=\") {\r\n          return this.$message({\r\n            message: \"未识别到车牌\",\r\n            type: \"warning\",\r\n          });\r\n        } else {\r\n          // 对车牌号进行解码\r\n          var carName = this.base64Decode(getData);\r\n\r\n          this.testCarNum(carName, \"自动获取\");\r\n        }\r\n\r\n        // this.carStatus = true;\r\n      } else {\r\n        this.dialogCarVisible = false;\r\n        this.carNum = \"\";\r\n        this.licensePlateUnit = [];\r\n      }\r\n    },\r\n    // 获取地泵数据\r\n    getDun() {\r\n      if (!this.isRuning) {\r\n        // 清除现有定时器\r\n        if (this.timer) {\r\n          clearInterval(this.timer);\r\n          this.timer = null;\r\n        }\r\n        this.timer = setInterval(() => {\r\n          // axios.get('http://192.168.1.144:8077/weight/getWeight', {\r\n          axios\r\n            .get(this.baseUrl + \"/weight/getWeight\", {\r\n              headers: {\r\n                \"Content-Type\": \"application/json; charset=UTF-8\",\r\n              },\r\n            })\r\n            .then((response) => {\r\n              // console.log(response.data.data, \"地泵数据\");\r\n\r\n              if (response.data.code == 200) {\r\n                this.weightErrorInfoNum = 0;\r\n                this.colorStyle.fontSize = \"30px\";\r\n                this.colorStyle.color = \"#409EFF\";\r\n\r\n                this.weight = response.data.data;\r\n                // this.zaizhongNum = 5;\r\n              } else if (response.data.code == 202) {\r\n                this.weightErrorInfoNum++;\r\n\r\n                if (this.weightErrorInfoNum >= 10) {\r\n                  // console.log(response);\r\n                  var resData = response.data.message.substring(0, 7);\r\n                  this.endData = response.data.message.substring(7);\r\n                  // console.log(resData, this.endData);\r\n                  this.colorStyle.fontSize = \"20px\";\r\n                  this.colorStyle.color = \"red\";\r\n                  this.weight = resData;\r\n                }\r\n\r\n                // console.log(this.weight, \"我是地泵数据66\");\r\n              }\r\n              // if (this.weight == \"载重信息无变化\") {\r\n              //   this.weightErrorInfoNum++;\r\n              //   // 如果载重信息无变化次数达到连续10次，则弹出提示框并重启地泵设备\r\n\r\n              //   if(this.weightErrorInfoNum >= 10 ){\r\n              //     clearInterval(this.timer);\r\n              //   this.isRuning = false;\r\n\r\n              //   this.$confirm(\r\n              //     \"载重信息无变化, 需要重启磅头及设备或联系管理人员！\",\r\n              //     \"提示\",\r\n              //     {\r\n              //       confirmButtonText: \"确定\",\r\n              //       cancelButtonText: \"取消\",\r\n              //       type: \"warning\",\r\n              //     }\r\n              //   )\r\n              //     .then(() => {\r\n              //       // this.zaizhongNum = 6;\r\n              //       this.weightErrorInfoNum = 0;\r\n              //       clearInterval(this.timer);\r\n              //       this.isRuning = false;\r\n              //       this.getDun();\r\n\r\n              //       console.log(this.zaizhongNum, \"我是重启次数\");\r\n              //       this.$message({\r\n              //         type: \"success\",\r\n              //         message: \"请马上重启磅头及相关设备！\",\r\n              //       });\r\n              //     })\r\n              //     .catch(() => {\r\n              //       // this.zaizhongNum = 5;\r\n              //       clearInterval(this.timer);\r\n              //       this.isRuning = false;\r\n              //       this.getDun();\r\n              //       this.$message({\r\n              //         type: \"info\",\r\n              //         message: \"已取消\",\r\n              //       });\r\n              //     });\r\n              //   }\r\n\r\n              // }\r\n            })\r\n            .catch((error) => {\r\n              // 处理错误，例如记录日志或显示错误消息\r\n              // console.error(\"请求失败:\", error);\r\n            });\r\n          // this.openhandle()\r\n        }, 1000);\r\n      }\r\n    },\r\n    base64Decode(str) {\r\n      // console.log(str, \"我是str\");\r\n      let binaryStr = atob(str);\r\n      let len = binaryStr.length;\r\n      let bytes = new Uint8Array(len);\r\n      for (let i = 0; i < len; i++) {\r\n        bytes[i] = binaryStr.charCodeAt(i);\r\n      }\r\n      let utf8decoder = new TextDecoder();\r\n      let decodedStr = utf8decoder.decode(bytes);\r\n      console.log(decodedStr, \"查看一下车牌号\"); // 输出“中文”\r\n      this.licensePlateUnit = decodedStr.split(\"\");\r\n\r\n      return decodedStr;\r\n    },\r\n    disconnectMqttClient() {\r\n      this.client.end(() => {\r\n        console.log(\"Disconnected from MQTT broker\");\r\n      });\r\n    },\r\n    disconnectMqttClient2() {\r\n      this.client2.end(() => {\r\n        console.log(\"Disconnected from MQTT broker\");\r\n      });\r\n    },\r\n    disconnectMqttClient3() {\r\n      this.client3.end(() => {\r\n        console.log(\"Disconnected from MQTT broker\");\r\n      });\r\n    },\r\n    // 手动触发抓拍\r\n    replayget() {\r\n      this.submitLoading = false;\r\n      // 第一个摄像头\r\n      var params = {\r\n        id: \"camera1\",\r\n        sn: this.equimentId1,\r\n        name: \"ivs_trigger\",\r\n        version: \"1.0\",\r\n        timestamp: **********,\r\n        payload: {\r\n          type: \"ivs_trigger\",\r\n          body: {},\r\n        },\r\n      };\r\n      // 第二个摄像头\r\n      var params2 = {\r\n        id: \"camera\",\r\n        sn: this.equimentId2,\r\n        name: \"ivs_trigger\",\r\n        version: \"1.0\",\r\n        timestamp: **********,\r\n        payload: {\r\n          type: \"ivs_trigger\",\r\n          body: {},\r\n        },\r\n      };\r\n      console.log(\"重新获取\");\r\n      // 发布手动触发抓拍\r\n      this.client.publish(\r\n        \"device/\" + this.equimentId1 + \"/message/down/ivs_trigger\",\r\n        JSON.stringify(params)\r\n      );\r\n      console.log(this.equimentId2, params, \"我是第二个摄像头id\");\r\n      this.client2.publish(\r\n        \"device/\" + this.equimentId2 + \"/message/down/ivs_trigger\",\r\n        JSON.stringify(params2)\r\n      );\r\n    },\r\n\r\n    getvideo() {\r\n      // this.getClient()\r\n      var data = {\r\n        type: \"get_live_stream_type\",\r\n        module: \"BUS_WEB_REQUEST\",\r\n      };\r\n\r\n      console.log(\r\n        \"http://\" + this.ClientData.cameras[0].ip + \"/request.php\",\r\n        \"查看地址\"\r\n      );\r\n      // axios.post('http://*************/request.php', data).then(res => {\r\n      axios\r\n        .post(\"http://\" + this.ClientData.cameras[0].ip + \"/request.php\", data)\r\n        .then((res) => {\r\n          // console.log(res.data.body.token, \"我数据tokern\");\r\n          this.token = res.data.body.token;\r\n          if (flvjs.isSupported()) {\r\n            this.videoEl = document.getElementById(\"videoEl\");\r\n            // 创建播放器实例\r\n            this.flvPlayer = flvjs.createPlayer({\r\n              type: \"flv\",\r\n              // url: `ws://*************:9080/ws.flv?token=${this.token}&channel=0`\r\n              url: `ws://${this.ClientData.cameras[0].ip}:9080/ws.flv?token=${this.token}&channel=0`,\r\n            });\r\n            // 挂载元素\r\n            this.flvPlayer.attachMediaElement(this.videoEl);\r\n            // 监听错误事件\r\n            this.flvPlayer.on(flvjs.Events.ERROR, (err, errdet) => {\r\n              // 参数 err 是一级异常，errdet 是二级异常\r\n              if (err == flvjs.ErrorTypes.MEDIA_ERROR) {\r\n                console.log(\"媒体错误\");\r\n                if (errdet == flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {\r\n                  console.log(\"媒体格式不支持\");\r\n                }\r\n              } else if (err == flvjs.ErrorTypes.NETWORK_ERROR) {\r\n                console.log(\"网络错误\");\r\n                // 重新请求token\r\n\r\n                if (errdet == flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {\r\n                  console.log(\"http状态码异常\");\r\n                }\r\n              } else if (err == flvjs.ErrorTypes.OTHER_ERROR) {\r\n                console.log(\"其他异常：\", errdet);\r\n              }\r\n            });\r\n            // 加载流\r\n            this.flvPlayer.load();\r\n            // 播放流\r\n\r\n            // this.flvPlayer.play();\r\n          } else {\r\n            console.log(\"FLV.js is not supported in this browser.\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误，例如记录日志或显示错误消息\r\n          console.error(\"请求失败:\", error);\r\n        });\r\n    },\r\n    getvideo2() {\r\n      var data = {\r\n        type: \"get_live_stream_type\",\r\n        module: \"BUS_WEB_REQUEST\",\r\n      };\r\n      axios\r\n        .post(\"http://\" + this.ClientData.cameras[1].ip + \"/request.php\", data)\r\n        .then((res) => {\r\n          // console.log(res.data.body.token, \"我数据tokern\");\r\n          this.token2 = res.data.body.token;\r\n          if (flvjs.isSupported()) {\r\n            this.videoE2 = document.getElementById(\"videoE2\");\r\n            // 创建播放器实例\r\n            this.flvPlayer2 = flvjs.createPlayer({\r\n              type: \"flv\",\r\n              url: `ws://${this.ClientData.cameras[1].ip}:9080/ws.flv?token=${this.token2}&channel=0`,\r\n            });\r\n            // 挂载元素\r\n            this.flvPlayer2.attachMediaElement(this.videoE2);\r\n            // 监听错误事件\r\n            this.flvPlayer2.on(flvjs.Events.ERROR, (err, errdet) => {\r\n              // 参数 err 是一级异常，errdet 是二级异常\r\n              if (err == flvjs.ErrorTypes.MEDIA_ERROR) {\r\n                console.log(\"媒体错误\");\r\n                if (errdet == flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {\r\n                  console.log(\"媒体格式不支持\");\r\n                }\r\n              } else if (err == flvjs.ErrorTypes.NETWORK_ERROR) {\r\n                console.log(\"网络错误\");\r\n                // 重新请求token\r\n                if (errdet == flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {\r\n                  console.log(\"http状态码异常\");\r\n                }\r\n              } else if (err == flvjs.ErrorTypes.OTHER_ERROR) {\r\n                console.log(\"其他异常：\", errdet);\r\n              }\r\n            });\r\n            // 加载流\r\n            this.flvPlayer2.load();\r\n            // 播放流\r\n          } else {\r\n            console.log(\"FLV.js is not supported in this browser.\");\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          // 处理错误，例如记录日志或显示错误消息\r\n          console.error(\"请求失败:\", error);\r\n        });\r\n    },\r\n    flv_start() {\r\n      console.log(\"111\");\r\n      if (this.shebei1Status) {\r\n        this.getvideo();\r\n      }\r\n      this.buttonStatus = true;\r\n      this.flvPlayer.attachMediaElement(this.videoEl);\r\n      this.flvPlayer.unload();\r\n      // 监听错误事件\r\n      this.flvPlayer.on(flvjs.Events.ERROR, (err, errdet) => {\r\n        // 参数 err 是一级异常，errdet 是二级异常\r\n        if (err == flvjs.ErrorTypes.MEDIA_ERROR) {\r\n          console.log(\"媒体错误\");\r\n          if (errdet == flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {\r\n            console.log(\"媒体格式不支持\");\r\n          }\r\n        } else if (err == flvjs.ErrorTypes.NETWORK_ERROR) {\r\n          console.log(\"网络错误\");\r\n          if (errdet == flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {\r\n            console.log(\"http状态码异常\");\r\n          }\r\n        } else if (err == flvjs.ErrorTypes.OTHER_ERROR) {\r\n          console.log(\"其他异常：\", errdet);\r\n        }\r\n      });\r\n      this.flvPlayer.load();\r\n      this.flvPlayer.play();\r\n    },\r\n    flv_start2() {\r\n      this.buttonStatus2 = true;\r\n      this.flvPlayer2.attachMediaElement(this.videoE2);\r\n      this.flvPlayer2.unload();\r\n      // 监听错误事件\r\n      this.flvPlayer2.on(flvjs.Events.ERROR, (err, errdet) => {\r\n        // 参数 err 是一级异常，errdet 是二级异常\r\n        if (err == flvjs.ErrorTypes.MEDIA_ERROR) {\r\n          console.log(\"媒体错误\");\r\n          if (errdet == flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {\r\n            console.log(\"媒体格式不支持\");\r\n          }\r\n        } else if (err == flvjs.ErrorTypes.NETWORK_ERROR) {\r\n          console.log(\"网络错误\");\r\n          if (errdet == flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {\r\n            console.log(\"http状态码异常\");\r\n          }\r\n        } else if (err == flvjs.ErrorTypes.OTHER_ERROR) {\r\n          console.log(\"其他异常：\", errdet);\r\n        }\r\n      });\r\n      this.flvPlayer2.load();\r\n      this.flvPlayer2.play();\r\n    },\r\n    flv_end() {\r\n      this.buttonStatus = false;\r\n      if (this.flvPlayer) {\r\n        this.flvPlayer.pause();\r\n        this.flvPlayer.unload();\r\n      }\r\n    },\r\n    flv_end2() {\r\n      this.buttonStatus2 = false;\r\n      if (this.flvPlayer2) {\r\n        this.flvPlayer2.pause();\r\n        this.flvPlayer2.unload();\r\n      }\r\n    },\r\n    flv_full() {\r\n      var dom = document.getElementById(\"videoEl\");\r\n      if (dom.requestFullscreen) {\r\n        dom.requestFullscreen();\r\n      } else if (dom.webkitRequestFullScreen) {\r\n        dom.webkitRequestFullScreen();\r\n      }\r\n    },\r\n    flv_full2() {\r\n      var dom = document.getElementById(\"videoE2\");\r\n      if (dom.requestFullscreen) {\r\n        dom.requestFullscreen();\r\n      } else if (dom.webkitRequestFullScreen) {\r\n        dom.webkitRequestFullScreen();\r\n      }\r\n    },\r\n\r\n    showHistory() {\r\n      this.dialogTableVisible = true;\r\n      this.tableState = true;\r\n      this.pageData.pageNumber = 1;\r\n      this.formInline.cardNumber = \"\";\r\n      this.getHistory();\r\n      this.formInline.cardNumber = \"\";\r\n    },\r\n  \r\n    handleClick(result) {\r\n      console.log(result, \"7777777\");\r\n      this.licensePlateUnit = result.split(\"\");\r\n      this.licensePlateUnitLength = this.licensePlateUnit.length;\r\n    },\r\n    handleSelect(event) {\r\n      // console.log(this.licensePlateUnit, \"查看变化前\");\r\n      // console.log(event.target.value, \"我是监听的值\");\r\n      this.licensePlateUnit[0] = event.target.value;\r\n      this.licensePlateUnitLength = this.licensePlateUnit.length;\r\n      this.licensePlateDoor = true;\r\n      console.log(\r\n        this.licensePlateUnit.length,\r\n        \"我是监听的值,licensePlateUnit的长度\"\r\n      );\r\n    },\r\n    license1(event) {\r\n      console.log(event, \"我是监听的值1\");\r\n      if (event.target.value) console.log(\"我进入了这个监听1\");\r\n      this.licensePlateUnit[1] = event.target.value;\r\n    },\r\n    license2(event) {\r\n      console.log(event, \"我是监听的值2\");\r\n      this.licensePlateUnit[2] = event.target.value\r\n        .split(\"\")\r\n        .map((char) => {\r\n          if (/[a-zA-Z]/.test(char)) {\r\n            return char.toUpperCase();\r\n          } else if (/[0-9]/.test(char)) {\r\n            return char;\r\n          } else {\r\n            return char; // 对于非字母非数字的字符，保持不变\r\n          }\r\n        })\r\n        .join(\"\");\r\n    },\r\n    license3(event) {\r\n      console.log(event, \"我是监听的值\");\r\n      this.licensePlateUnit[3] = event.target.value;\r\n    },\r\n    license4(event) {\r\n      console.log(event, \"我是监听的值\");\r\n      this.licensePlateUnit[4] = event.target.value;\r\n    },\r\n    license5(event) {\r\n      console.log(event, \"我是监听的值\");\r\n      this.licensePlateUnit[5] = event.target.value;\r\n    },\r\n    license6(event) {\r\n      console.log(event, \"我是监听的值\");\r\n      this.licensePlateUnit[6] = event.target.value;\r\n    },\r\n    license7(event) {\r\n      console.log(event, \"我是监听的值\");\r\n      this.licensePlateUnit[7] = event.target.value;\r\n    },\r\n    pickOn(value) {\r\n      console.log(this.licensePlateUnit.join(\"\"), \"我是选中的值66\");\r\n\r\n      this.licensePlateDoor = true;\r\n      if (this.licensePlateUnit.length <= 7) {\r\n        this.licensePlateUnit.push(value);\r\n        this.licensePlateUnitLength = this.licensePlateUnit.length;\r\n        this.getmuhuSearch(this.licensePlateUnit.join(\"\"));\r\n      }\r\n    },\r\n    delCarNo() {\r\n      console.log(this.licensePlateUnit, \"888888888888\");\r\n      this.licensePlateUnit.pop();\r\n\r\n      // console.log(this.licensePlateUnit.join(\"\"), \"我是选中的值66\");\r\n      this.getmuhuSearch(this.licensePlateUnit.join(\"\"));\r\n      if (this.licensePlateUnit.length === 0) {\r\n        this.submitLoading = false;\r\n        this.licensePlateDoor = false;\r\n        this.licensePlateUnitLength = 0;\r\n        this.carNum = \"\";\r\n      }\r\n    },\r\n    close() {\r\n      this.getmuhuSearch(\"\");\r\n      this.submitLoading = false;\r\n      this.licensePlateDoor = false;\r\n      this.licensePlateUnit = [];\r\n      this.licensePlateUnitLength = 0;\r\n      this.carNum = \"\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n.footerCard {\r\n  height: 100px;\r\n  width: 100%;\r\n  background-color: #fff;\r\n  padding-top: 10px;\r\n}\r\n\r\n/* 滚动条整体部分 */\r\n::-webkit-scrollbar {\r\n  width: 6px;\r\n  /* 横向滚动条的宽度 */\r\n  height: 6px;\r\n  /* 纵向滚动条的高度 */\r\n  background-color: #ebeef5;\r\n  /* 滚动条背景色 */\r\n}\r\n\r\n/* 滚动条里面的滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  background-color: #ccc;\r\n  /* 滑块背景色 */\r\n  border-radius: 3px;\r\n  /* 滑块圆角 */\r\n  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);\r\n  /* 滑块内阴影 */\r\n}\r\n\r\n/* 滚动条的轨道 */\r\n::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  /* 轨道背景 */\r\n  border-radius: 3px;\r\n  /* 轨道圆角 */\r\n  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\r\n  /* 轨道内阴影 */\r\n}\r\n\r\n.el-table thead {\r\n  color: #000;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-switch__label * {\r\n  font-size: 18px !important;\r\n}\r\n\r\n.el-select .el-input {\r\n  border: none !important;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 美化下拉箭头 */\r\n.el-select .el-input .el-select__icon {\r\n  color: rgb(18, 2, 236);\r\n}\r\n\r\n/* 美化下拉选项 */\r\n.el-select-dropdown .el-scrollbar .el-select-dropdown__item {\r\n  border-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.el-select-dropdown .el-scrollbar .el-select-dropdown__item.selected {\r\n  background-color: #f0f7fa;\r\n  font-weight: bold;\r\n}\r\n\r\n.el-select-dropdown .el-scrollbar .el-select-dropdown__item:hover {\r\n  background-color: #e6f1fe;\r\n}\r\n\r\n.carNoBoxInputTrue {\r\n  color: red;\r\n}\r\n\r\n.carNoBoxInputFalse {\r\n  color: #2dd866;\r\n}\r\n\r\n.el-descriptions__body {\r\n  color: #333 !important;\r\n  background-color: #fff;\r\n}\r\n\r\nbody {\r\n  background: linear-gradient(\r\n    180deg,\r\n    rgba(72, 170, 255, 0.6) 0%,\r\n    rgba(10, 148, 251, 0.1) 78%,\r\n    rgba(0, 28, 56, 0.04) 100%\r\n  );\r\n}\r\n\r\n.el-descriptions-item__label:not(.is-bordered-label) {\r\n  margin-right: 10px;\r\n  align-items: center !important;\r\n  width: 60px;\r\n}\r\n\r\nvideo {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\nvideo,\r\n.parent-element {\r\n  border: none;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 所有控件 */\r\nvideo::-webkit-media-controls-enclosure {\r\n  display: none;\r\n}\r\n\r\n/* 进度条 */\r\nvideo::-webkit-media-controls-timeline {\r\n  display: none;\r\n}\r\n\r\nvideo::-webkit-media-controls-current-time-display {\r\n  display: none;\r\n}\r\n\r\n/* 音量按钮 */\r\nvideo::-webkit-media-controls-mute-button {\r\n  display: none;\r\n}\r\n\r\nvideo::-webkit-media-controls-toggle-closed-captions-button {\r\n  display: none;\r\n}\r\n\r\n/* 音量的控制条 */\r\nvideo::-webkit-media-controls-volume-slider {\r\n  display: none;\r\n}\r\n\r\n/*  播放按钮 */\r\nvideo::-webkit-media-controls-play-button {\r\n  display: none;\r\n}\r\n\r\nul {\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.carNo {\r\n  border-radius: 6px;\r\n  background: #fff;\r\n  font-weight: bold;\r\n  font-size: 20px;\r\n  height: 28px;\r\n  width: 28px;\r\n  margin: 6px;\r\n  padding: 12px;\r\n  cursor: pointer;\r\n}\r\n\r\n.inputBox {\r\n  color: white;\r\n  height: 30px;\r\n  line-height: 30px;\r\n  width: 30px;\r\n  font-size: 28px;\r\n  text-align: center;\r\n  background-color: transparent;\r\n  border: none;\r\n  outline: none;\r\n  caret-color: rgba(0, 0, 0, 0);\r\n}\r\n\r\ninput:focus {\r\n  border-bottom: 3px solid #fff;\r\n  transition: all 0.5s;\r\n}\r\n\r\n.el-radio-button__inner {\r\n  border: 1px solid #DCDFE6 !important;\r\n  box-shadow: 0 !important;\r\n}\r\n\r\n.dot {\r\n  margin-bottom: 6px;\r\n  background-color: #fff;\r\n  height: 6px;\r\n  width: 6px;\r\n  border-radius: 50%;\r\n  display: inline-block;\r\n}\r\n\r\n.delBt {\r\n  background: #acb3bb;\r\n  border-radius: 6px;\r\n  display: inline-block;\r\n  font-weight: bold;\r\n  font-size: 20px;\r\n  height: 28px;\r\n  width: 28px;\r\n  margin: 6px;\r\n  padding: 12px;\r\n  cursor: pointer;\r\n}\r\n\r\n.carNoBoxInput {\r\n  display: flex;\r\n  width: 310px;\r\n  align-items: center;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  margin: 6px 0;\r\n  background: #2d66d8;\r\n}\r\n\r\n.carNoBoxInput1 {\r\n  display: flex;\r\n  width: 310px;\r\n  align-items: center;\r\n  height: 80px;\r\n  border-radius: 8px;\r\n  margin: 6px 0;\r\n  background: #2dd866;\r\n}\r\n\r\n.carNoBox {\r\n  background: #d0d5d9;\r\n  position: relative;\r\n  width: 600px;\r\n  border-radius: 6px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-items: center;\r\n  align-items: center;\r\n}\r\n\r\n.box-card1 {\r\n  width: 600px;\r\n  height: 91vh;\r\n}\r\n\r\n.el-header,\r\n.el-footer {\r\n  /* background-color: #B3C0D1; */\r\n  color: #333;\r\n  text-align: center;\r\n  /* line-height: 50px; */\r\n}\r\n\r\n.el-aside {\r\n  background-color: #d3dce6;\r\n  color: #333;\r\n  /* text-align: center; */\r\n  height: 91vh;\r\n}\r\n\r\n.el-main {\r\n  background-color: #e9eef3;\r\n  color: #333;\r\n  /* text-align: center; */\r\n  /* line-height: 160px; */\r\n  height: 80vh;\r\n}\r\n\r\nbody > .el-container {\r\n  margin-bottom: 40px;\r\n}\r\n\r\n.el-container:nth-child(5) .el-aside,\r\n.el-container:nth-child(6) .el-aside {\r\n  line-height: 260px;\r\n}\r\n\r\n.el-container:nth-child(7) .el-aside {\r\n  line-height: 320px;\r\n}\r\n\r\n.el-card__body,\r\n.el-main {\r\n  padding-left: 15px;\r\n}\r\n\r\n/*  */\r\n\r\n.image-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-around;\r\n  gap: 20px;\r\n  padding: 20px;\r\n}\r\n\r\n.image-wrapper {\r\n  flex: 1;\r\n  min-width: 300px;\r\n  max-width: 45%;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.image-wrapper:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.preview-image {\r\n  width: 100%;\r\n  height: 300px;\r\n  display: block;\r\n  cursor: pointer;\r\n}\r\n\r\n.image-caption {\r\n  padding: 15px;\r\n  text-align: center;\r\n  background: #f5f7fa;\r\n  color: #606266;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.dota {\r\n  display: inline-block;\r\n  width: 3em;\r\n  text-align: left;\r\n  animation: dota 1.5s infinite step-start;\r\n}\r\n\r\n@keyframes dota {\r\n  0% {\r\n    content: \".\";\r\n  }\r\n\r\n  33% {\r\n    content: \"..\";\r\n  }\r\n\r\n  66% {\r\n    content: \"...\";\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./main.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./main.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./main.vue?vue&type=template&id=217b86d8\"\nimport script from \"./main.vue?vue&type=script&lang=js\"\nexport * from \"./main.vue?vue&type=script&lang=js\"\nimport style0 from \"./main.vue?vue&type=style&index=0&id=217b86d8&prod&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _vm._m(0)\n}\nvar staticRenderFns = [function (){var _vm=this,_c=_vm._self._c;return _c('div',{attrs:{\"id\":\"app\"}},[_c('div',{attrs:{\"id\":\"loader-wrapper\"}},[_c('div',{attrs:{\"id\":\"loader\"}}),_c('div',{staticClass:\"loader-section section-left\"}),_c('div',{staticClass:\"class=\",attrs:{\"loader-section\":\"\",\"section-right\":\"\"}}),_c('div',{staticClass:\"load_title\"},[_vm._v(\" 正在启动系统中，请耐心等待 \")])])])\n}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div id=\"app\">\r\n    <div id=\"loader-wrapper\">\r\n      <div id=\"loader\">\r\n\r\n      </div>\r\n      <div class=\"loader-section section-left\">\r\n\r\n      </div>\r\n      <div class=\"class=\" loader-section section-right>\r\n\r\n      </div>\r\n      <div class=\"load_title\">\r\n        正在启动系统中，请耐心等待\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport axios from \"axios\";\r\n  export default {\r\n    data() {\r\n      return {\r\n       \r\n      }\r\n    },\r\n    mounted() {\r\n      // var that = this;\r\n      // setTimeout(function() {\r\n      //   that.$router.push('/home');\r\n      // }, 3000)\r\n      \r\n    },\r\n    created() {\r\n      this.getClint();\r\n    },\r\n    methods: {\r\n      getClint(){\r\n        console.log(66666666)\r\n        axios\r\n          .get(\"http://127.0.0.1:8077/car/getProjectId\")\r\n          .then((res) => {\r\n            // console.log(res, \"查看请求的信息!!!!666\");\r\n            console.log(res)\r\n            if(res.data.code==0){\r\n              this.$router.push('/main');\r\n            }else{\r\n              this.getClint()\r\n            }\r\n\r\n          })\r\n          .catch((error) => {\r\n            this.getClint()\r\n            // 处理错误，例如记录日志或显示错误消息\r\n            console.error(\"请求失败:\", error);\r\n          });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n<style>\r\n #app {\r\n   height: 100%;\r\n   margin: 0px;\r\n   padding: 0px;\r\n }\r\n\r\n .chromeframe {\r\n   margin: 0.2em 0;\r\n   background: #ccc;\r\n   color: #000;\r\n   padding: 0.2em 0;\r\n }\r\n\r\n #loader-wrapper {\r\n   position: fixed;\r\n   top: 0;\r\n   left: 0;\r\n   width: 100%;\r\n   height: 100%;\r\n   z-index: 999999;\r\n }\r\n\r\n #loader {\r\n   display: block;\r\n   position: relative;\r\n   left: 50%;\r\n   top: 50%;\r\n   width: 150px;\r\n   height: 150px;\r\n   margin: -75px 0 0 -75px;\r\n   border-radius: 50%;\r\n   border: 3px solid transparent;\r\n   border-top-color: #FFF;\r\n   -webkit-animation: spin 2s linear infinite;\r\n   -ms-animation: spin 2s linear infinite;\r\n   -moz-animation: spin 2s linear infinite;\r\n   -o-animation: spin 2s linear infinite;\r\n   animation: spin 2s linear infinite;\r\n   z-index: 1001;\r\n }\r\n\r\n #loader:before {\r\n   content: \"\";\r\n   position: absolute;\r\n   top: 5px;\r\n   left: 5px;\r\n   right: 5px;\r\n   bottom: 5px;\r\n   border-radius: 50%;\r\n   border: 3px solid transparent;\r\n   border-top-color: #FFF;\r\n   -webkit-animation: spin 3s linear infinite;\r\n   -moz-animation: spin 3s linear infinite;\r\n   -o-animation: spin 3s linear infinite;\r\n   -ms-animation: spin 3s linear infinite;\r\n   animation: spin 3s linear infinite;\r\n }\r\n\r\n #loader:after {\r\n   content: \"\";\r\n   position: absolute;\r\n   top: 15px;\r\n   left: 15px;\r\n   right: 15px;\r\n   bottom: 15px;\r\n   border-radius: 50%;\r\n   border: 3px solid transparent;\r\n   border-top-color: #FFF;\r\n   -moz-animation: spin 1.5s linear infinite;\r\n   -o-animation: spin 1.5s linear infinite;\r\n   -ms-animation: spin 1.5s linear infinite;\r\n   -webkit-animation: spin 1.5s linear infinite;\r\n   animation: spin 1.5s linear infinite;\r\n }\r\n\r\n\r\n @-webkit-keyframes spin {\r\n   0% {\r\n     -webkit-transform: rotate(0deg);\r\n     -ms-transform: rotate(0deg);\r\n     transform: rotate(0deg);\r\n   }\r\n\r\n   100% {\r\n     -webkit-transform: rotate(360deg);\r\n     -ms-transform: rotate(360deg);\r\n     transform: rotate(360deg);\r\n   }\r\n }\r\n\r\n @keyframes spin {\r\n   0% {\r\n     -webkit-transform: rotate(0deg);\r\n     -ms-transform: rotate(0deg);\r\n     transform: rotate(0deg);\r\n   }\r\n\r\n   100% {\r\n     -webkit-transform: rotate(360deg);\r\n     -ms-transform: rotate(360deg);\r\n     transform: rotate(360deg);\r\n   }\r\n }\r\n\r\n\r\n #loader-wrapper .loader-section {\r\n   position: fixed;\r\n   top: 0;\r\n   width: 100%;\r\n   height: 100%;\r\n   /*background-image: url(\"image/loginBackground.jpg\");*/\r\n   /*background-position: center center  ;*/\r\n   /*background-repeat: no-repeat;*/\r\n   /*background-size: cover;*/\r\n  background: linear-gradient(to top, #3C8DBC, white);\r\n   \r\n   /* background: linear-gradient(top, #3C8DBC, white);\r\n   background: -ms-linear-gradient(top, #3C8DBC, white);\r\n   background: -webkit-linear-gradient(top, #3C8DBC, white);\r\n   background: -moz-linear-gradient(top, #3C8DBC, white); */\r\n   z-index: 1000;\r\n   -webkit-transform: translateX(0);\r\n   -ms-transform: translateX(0);\r\n   transform: translateX(0);\r\n }\r\n\r\n #loader-wrapper .loader-section.section-left {\r\n   left: 0;\r\n }\r\n\r\n #loader-wrapper .loader-section.section-right {\r\n   right: 0;\r\n }\r\n\r\n\r\n .loaded #loader-wrapper .loader-section.section-left {\r\n   -webkit-transform: translateX(-100%);\r\n   -ms-transform: translateX(-100%);\r\n   transform: translateX(-100%);\r\n   -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);\r\n   transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);\r\n }\r\n\r\n .loaded #loader-wrapper .loader-section.section-right {\r\n   -webkit-transform: translateX(100%);\r\n   -ms-transform: translateX(100%);\r\n   transform: translateX(100%);\r\n   -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);\r\n   transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);\r\n }\r\n\r\n .loaded #loader {\r\n   opacity: 0;\r\n   -webkit-transition: all 0.3s ease-out;\r\n   transition: all 0.3s ease-out;\r\n }\r\n\r\n .loaded #loader-wrapper {\r\n   visibility: hidden;\r\n   -webkit-transform: translateY(-100%);\r\n   -ms-transform: translateY(-100%);\r\n   transform: translateY(-100%);\r\n   -webkit-transition: all 0.3s 1s ease-out;\r\n   transition: all 0.3s 1s ease-out;\r\n }\r\n\r\n .no-js #loader-wrapper {\r\n   display: none;\r\n }\r\n\r\n .no-js h1 {\r\n   color: #222222;\r\n }\r\n\r\n #loader-wrapper .load_title {\r\n   font-family: 'Open Sans';\r\n   /*color: #FFF;*/\r\n   color: #fff;\r\n   font-size: 19px;\r\n   width: 100%;\r\n   text-align: center;\r\n   z-index: 9999999999999;\r\n   position: absolute;\r\n   top: 60%;\r\n   opacity: 1;\r\n   line-height: 30px;\r\n }\r\n\r\n #loader-wrapper .load_title span {\r\n   font-weight: normal;\r\n   font-style: italic;\r\n   font-size: 13px;\r\n   color: #FFF;\r\n   opacity: 0.5;\r\n }\r\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./one.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./one.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./one.vue?vue&type=template&id=c8243c88\"\nimport script from \"./one.vue?vue&type=script&lang=js\"\nexport * from \"./one.vue?vue&type=script&lang=js\"\nimport style0 from \"./one.vue?vue&type=style&index=0&id=c8243c88&prod&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/cli-service/node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from \"vue\";\r\nimport Router from \"vue-router\";\r\nimport Main from \"@/components/main.vue\";\r\nimport One from \"@/components/one.vue\";\r\n// import About from \"@/components/About.vue\";\r\nVue.use(Router);\r\nexport default new Router({\r\n  mode: \"history\", // 使用HTML5 History模式\r\n  routes: [\r\n    {\r\n      path: \"/\",\r\n      //   name: \"Main\",\r\n      component: Main,\r\n    },\r\n    {\r\n      path: \"/main\",\r\n      //   name: \"Main\",\r\n      component: Main,\r\n    },\r\n    // {\r\n    //   path: \"/about\",\r\n    //   name: \"About\",\r\n    //   component: About,\r\n    // },\r\n  ],\r\n});", "import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from \"@/utils/router\";; // 引入路由配置\r\nimport ElementUI from \"element-ui\";\r\nimport \"element-ui/lib/theme-chalk/index.css\";\r\n// require(\"dotenv\").config({ path: \".env.development\" });\r\nVue.config.productionTip = false\r\nVue.use(ElementUI);\r\nnew Vue({\r\n  router,\r\n  render: (h) => h(App),\r\n  strict: false,\r\n}).$mount(\"#app\");\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.amdO = {};", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = function(module) {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"/\";", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkquarryweb\"] = self[\"webpackChunkquarryweb\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(1582); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["render", "_vm", "this", "_c", "_self", "attrs", "staticRenderFns", "name", "component", "staticStyle", "_v", "staticClass", "slot", "on", "SX", "TG", "ref", "buttonStatus", "flv_start", "_e", "flv_end", "flv_full", "buttonStatus2", "flv_start2", "flv_end2", "flv_full2", "class", "car<PERSON>tatus", "_s", "showHistory", "ClientData", "client", "projectName", "stationName", "licensePlateUnit", "length", "domProps", "$event", "licensePlateDoor", "handleSelect", "license1", "license2", "license3", "license4", "license5", "license6", "license7", "require", "_l", "firstSixItems", "result", "key", "handleClick", "licensePlateUnitLength", "columns", "item", "pickOn", "delCarNo", "numberColumns", "confirm", "close", "style", "colorStyle", "weight", "endData", "handleChangeType", "model", "value", "callback", "$$v", "expression", "leftUrl", "rightUrl", "updateState", "updateButton", "updateText", "replayget", "submitLoading", "submitData", "dialogTableVisible", "formInline", "nativeOn", "preventDefault", "getHistory", "type", "indexOf", "_k", "keyCode", "cardNumber", "$set", "pickerOptions", "searchTime", "guobengTime", "tableState", "directives", "rawName", "loading", "gridData", "scopedSlots", "_u", "fn", "scope", "row", "carNumber", "createTime", "toFixed", "dataType", "isPull", "phoneLoading", "handleEdit", "$index", "pageData", "total", "pageNumber", "handleSizeChange", "handleCurrentChange", "dialogCarVisible", "handleClose", "showClose", "showCar", "carTrue", "dialogCarIsVisible", "CarisFalse", "dialogVisibleOne", "handleCloseOne", "dialogVisibleTwe", "handleCloseTwe", "dialogVisibleThree", "dialogVisibleFore", "centerDialogVisible", "urlimagL", "srcList", "urlimagR", "CryptoJS", "Utf8", "parse", "components", "data", "today", "Date", "startTime", "setHours", "endTime", "localDate", "dayjs", "format", "localDate1", "tgid", "tgId", "cremarsId", "fontSize", "weightErrorInfoNum", "endDatas", "shortcuts", "text", "onClick", "picker", "end", "start", "setTime", "getTime", "$emit", "client3", "client2", "pageSize", "stationId", "weightInfo", "carIs", "requestToken", "reader", "port", "writer", "receivedData", "errorMessage", "payload", "shebei1Status", "shebei2Status", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "xintiaobao2", "videoEl", "videoE2", "carNum", "carNumPassword", "flvPlayer", "flvPlayer2", "equimentId1", "equimentId2", "equimentId3", "baseUrl", "mqttIp", "licensePlate", "searchResults", "activeIndex", "initData", "carValue", "commitCishu", "zaizhongNum", "timer", "isRuning", "taigancarNumber", "daozhaValue", "created", "window", "addEventListener", "connectMqttBroker", "connectMqttBroker2", "connectMqttBroker3", "console", "info", "getClient", "getDun", "navigator", "setInterval", "timestamp", "now", "<PERSON><PERSON><PERSON>S<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pause", "unload", "detachMediaElement", "destroy", "watch", "newValue", "oldValue", "res", "clientId", "id", "axios", "post", "then", "log", "$message", "success", "catch", "err", "error", "unsubscribe", "qos", "computed", "slice", "methods", "get", "params", "headImage", "rearImage", "val", "fullscreenBig", "document", "documentElement", "requestFullscreen", "webkitRequestFullscreen", "mozRequestFullScreen", "msRequestFullscreen", "cameras", "sn", "getvideo", "getvideo2", "attachMediaElement", "flvjs", "ERROR", "errdet", "MEDIA_ERROR", "MEDIA_FORMAT_UNSUPPORTED", "NETWORK_ERROR", "NETWORK_STATUS_CODE_INVALID", "OTHER_ERROR", "load", "play", "disconnectMqttClient", "disconnectMqttClient2", "disconnectMqttClient3", "mqtt", "connect", "protocol", "hostname", "path", "username", "password", "keepAliveInterval", "mqttVersion", "parseInt", "Math", "random", "subscribe", "granted", "topic", "message", "JSON", "toString", "AlarmInfoPlate", "PlateResult", "full_image_content", "triggerType", "is_fake_plate", "base64Decode", "license", "getData", "carName", "testCarNum", "zdImg", "daozha_testCarNum", "includes", "push", "handle_Taigan", "setTimeout", "remove", "filter", "stringify", "encrypt", "headers", "token", "datas", "cartype", "some", "tgImg", "code", "liftRodDataVo", "<PERSON><PERSON><PERSON>", "join", "location", "reload", "params3", "version", "body", "publish", "delay", "io", "searchCardNum", "response", "num", "getProjectInfo", "process", "VUE_APP_BASE_URL", "list", "buttoTtype", "match", "match2", "matchBase64Left", "matchBase64Left2", "weighingDataVo", "numberStr", "undefined", "encrypted", "mode", "ECB", "padding", "Pkcs7", "getToken_", "appId", "secret", "handleCloseThree", "handleCloseFore", "clearInterval", "color", "resData", "substring", "str", "binaryStr", "atob", "len", "bytes", "Uint8Array", "i", "charCodeAt", "utf8decoder", "TextDecoder", "decodedStr", "decode", "split", "params2", "module", "ip", "getElementById", "url", "token2", "dom", "webkitRequestFullScreen", "event", "target", "map", "char", "test", "toUpperCase", "pop", "_m", "mounted", "getClint", "$router", "<PERSON><PERSON>", "use", "Router", "routes", "Main", "config", "productionTip", "ElementUI", "router", "h", "App", "strict", "$mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "loaded", "__webpack_modules__", "call", "m", "amdO", "deferred", "O", "chunkIds", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "Object", "keys", "every", "splice", "r", "n", "getter", "__esModule", "d", "a", "definition", "o", "defineProperty", "enumerable", "g", "globalThis", "Function", "e", "obj", "prop", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "nmd", "paths", "children", "p", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "chunkLoadingGlobal", "self", "for<PERSON>ach", "bind", "__webpack_exports__"], "sourceRoot": ""}
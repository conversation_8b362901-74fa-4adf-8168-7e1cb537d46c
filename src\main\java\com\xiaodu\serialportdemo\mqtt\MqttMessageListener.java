package com.xiaodu.serialportdemo.mqtt;

import com.alibaba.fastjson.JSONArray;
import com.xiaodu.serialportdemo.dto.TempDataPoolDo;
import com.xiaodu.serialportdemo.mapper.SqLiteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;


import java.util.List;

@Component
public class MqttMessageListener {
    @Autowired
    @Lazy
    private SqLiteMapper mapper;

    @ServiceActivator(inputChannel = "mqttInputChannel")
    @Bean
    public MessageHandler mqttMessageHandler() {
        return new MessageHandler() {
            @Override
            public void handleMessage(Message<?> message) throws MessagingException {
                String payload = message.getPayload().toString(); // 获取消息内容
//                System.out.println("Received MQTT message: " + payload);

                try {
                    // 解析消息内容为 List<String>
                    List<String> carNumberList = JSONArray.parseArray(payload, String.class);

                    // 循环插入数据库
                    for (String carNumber : carNumberList) {
                        TempDataPoolDo tempDataPoolDo = new TempDataPoolDo();
                        tempDataPoolDo.setCarNumber(carNumber.trim()); // 去掉可能的空格
                        mapper.insert(tempDataPoolDo); // 插入数据库
//                        System.out.println("Inserted car number: " + carNumber);
                    }
                } catch (Exception e) {
                    e.printStackTrace(); // 捕获解析异常
                }
            }
        };
    }
}

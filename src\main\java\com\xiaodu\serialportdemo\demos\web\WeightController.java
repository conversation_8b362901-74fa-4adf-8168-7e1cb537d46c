package com.xiaodu.serialportdemo.demos.web;

/**
 * @Author: zt
 * @CreateTime: 2025-01-18
 * @Version: 1.0
 */

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fazecast.jSerialComm.SerialPort;
import com.xiaodu.serialportdemo.demos.DataAvailableListener;
import com.xiaodu.serialportdemo.demos.SerialPortManager;
import com.xiaodu.serialportdemo.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestController
@RequestMapping("/weight")
@Api(tags = "地磅接口")
//@CrossOrigin(origins = "*")
public class WeightController {

    // 状态常量
    private static final int STATUS_ERROR = -999999;      // 串口数据异常
    private static final BigDecimal SCALE_FACTOR = new BigDecimal(1000);
    
    // 预编译正则表达式，提高性能
    private static final Pattern DATA_PATTERN = Pattern.compile("\\u0002([+-]?\\d{6})");

    @Autowired
    @Lazy
    private CarController controller;

    // 使用AtomicInteger保证线程安全
    private final AtomicInteger integerValue = new AtomicInteger(STATUS_ERROR);
    private final AtomicReference<BigDecimal> result = new AtomicReference<>(BigDecimal.ZERO);
    private final AtomicInteger lastProcessedValue = new AtomicInteger(STATUS_ERROR);
    private volatile long lastDataReceiveTime = System.currentTimeMillis();
    private volatile boolean isReconnecting = false; // 重连状态标识
    private volatile long lastReconnectTime = 0; // 上次重连时间
    private volatile long dataReceivedCount = 0; // 接收数据计数
    private volatile long listenerTriggerCount = 0; // 监听器触发计数
    private final Object reconnectLock = new Object(); // 重连锁，确保串行执行
    private SerialPort serialPort;

    /**
     * 获取重量
     */
    @GetMapping("/getWeight")
    @ApiOperation("获取地磅重量")
    public Result<BigDecimal> getWeight() {
        // 兜底检查：验证串口物理状态和监听器健康状态
        if (integerValue.get() != STATUS_ERROR) {
            // 检查串口物理连接状态
            if (!SerialPortManager.isPortHealthy(serialPort)) {
                log.error("串口物理连接已断开，设置错误状态");
                integerValue.set(STATUS_ERROR);
            } else {
                // 检查数据接收超时（30秒无数据认为异常）
                long timeSinceLastData = System.currentTimeMillis() - lastDataReceiveTime;
                if (timeSinceLastData > 30000) {
                    log.warn("超过30秒未接收到数据，监听器可能失效 (距离上次数据{}秒)", timeSinceLastData / 1000);
                    integerValue.set(STATUS_ERROR);
                }
            }
        }

        int currentValue = integerValue.get();

        if (currentValue == STATUS_ERROR) {
            // 检查是否已经在重连中
            if (isReconnecting) {
                return Result.build(null, Result.ResultCodeEnum.ERROR);
            }
            
            // 防抖：避免频繁重连，至少间隔5秒
            long timeSinceLastReconnect = System.currentTimeMillis() - lastReconnectTime;
            if (timeSinceLastReconnect < 5000) {
                return Result.build(null, Result.ResultCodeEnum.ERROR);
            }
            
            reconnectSerialPort();
            return Result.build(null, Result.ResultCodeEnum.ERROR);
        }

        // 优化：只有当值发生变化时才重新计算
        int lastValue = lastProcessedValue.get();
        if (currentValue != lastValue) {
            // 数据有变化，重新计算
            BigDecimal calculatedResult = new BigDecimal(currentValue)
                .divide(SCALE_FACTOR, 3, java.math.RoundingMode.HALF_UP);
            
            result.set(calculatedResult);
            lastProcessedValue.set(currentValue);
            log.info("重量值更新: {}kg", calculatedResult);
        }

        return Result.ok(result.get());
    }

    /**
     * 获取监听器状态信息（用于调试）
     */
    @GetMapping("/listenerStatus")
    @ApiOperation("获取监听器状态")
    public Result<String> getListenerStatus() {
        long timeSinceLastData = System.currentTimeMillis() - lastDataReceiveTime;
        boolean portHealthy = SerialPortManager.isPortHealthy(serialPort);
        
        String status = String.format(
            "串口健康: %s | 重连状态: %s | 距离上次数据: %d秒 | 监听器触发: %d次 | 数据接收: %d次 | 当前值: %d",
            portHealthy, isReconnecting, timeSinceLastData / 1000, 
            listenerTriggerCount, dataReceivedCount, integerValue.get()
        );
        
        return Result.ok(status);
    }

    private void reconnectSerialPort() {
        // 使用同步锁确保只有一个线程执行重连
        synchronized (reconnectLock) {
            // 双重检查：防止在等待锁的过程中其他线程已经完成重连
            if (isReconnecting) {
                return;
            }
            
            try {
                log.info("开始串口重连流程");
                
                // 更新重连时间
                lastReconnectTime = System.currentTimeMillis();
                
                // 标记重连状态，阻止监听器处理数据
                isReconnecting = true;
                SerialPort oldPort = serialPort;
                serialPort = null; // 先设为null，防止监听器继续使用
                
                // 关闭旧串口（会自动清理监听器）
                SerialPortManager.closePort(oldPort);

                // 等待监听器线程结束和资源释放
                Thread.sleep(2000);

                // 重新启动
                start();
                
                log.info("串口重连完成");
            }catch (InterruptedException e){
                log.error("串口重连被中断", e);
                Thread.currentThread().interrupt();
            }catch (Exception e){
                log.error("串口重连失败", e);
            } finally {
                // 无论成功失败都要清除重连标识
                isReconnecting = false;
            }
        }
    }

    @PostConstruct
    public void start() {
        try {
            // 重置时间戳，避免启动时立即触发超时检查
            lastDataReceiveTime = System.currentTimeMillis();
            JSONObject projectId = controller.getProjectId();
            if (projectId == null) {
                throw new RuntimeException("未获取到项目id");
            }
            JSONObject client = projectId.getJSONObject("client");
//        com=串口号 baudRate=波特率 dataBit=数据位 stopBit=停止位 verifyBit=校验位
            String com = client.getString("com");
            if (StringUtils.isBlank(com)) {
                com = "COM1";
            }
//            log.info("串口号：{}", com);
            String baudRateString = client.getString("baudRate");
            String dataBitString = client.getString("dataBit");
            String stopBitString = client.getString("stopBit");
            String verifyBitString = client.getString("verifyBit");
            if (baudRateString == null || baudRateString.isEmpty()) {
                baudRateString = "1200";
            }
            int baudRate = Integer.parseInt(baudRateString);
//            log.info("波特率：{}", baudRate);
            if (dataBitString == null || dataBitString.isEmpty()) {
                dataBitString = "8";
            }
            int dataBit = Integer.parseInt(dataBitString);
//            log.info("数据位：{}", dataBit);
            if (stopBitString == null || stopBitString.isEmpty()) {
                stopBitString = "1";
            }
            int stopBit = Integer.parseInt(stopBitString);
//            log.info("停止位：{}", stopBit);
            if (verifyBitString == null || verifyBitString.isEmpty()) {
                verifyBitString = "0";
            }
            int verifyBit = Integer.parseInt(verifyBitString);
            log.info("串口配置: {}，波特率: {}", com, baudRate);
//            SerialPort serialPort = SerialPortManager.openPort(com, baudRate, dataBit, verifyBit, stopBit);
            serialPort = SerialPortManager.openPort(com, baudRate, dataBit, verifyBit, stopBit);

            if (serialPort == null) {
                log.error("串口{}打开失败，可能被占用", com);
                return;
            }

            //给当前串口对象设置监听器
            SerialPortManager.addListener(serialPort, new DataAvailableListener() {
                @Override
                public void dataAvailable() {
                    try {
                        // 增加监听器触发计数
                        listenerTriggerCount++;
                        
                        // 检查重连状态，避免在重连过程中处理数据
                        if (isReconnecting) {
                            return;
                        }
                        
                        // 使用成员变量而不是局部变量，避免闭包引用问题
                        SerialPort currentPort = WeightController.this.serialPort;
                        
                        // 检查串口状态，避免在重连过程中处理数据
                        if (!SerialPortManager.isPortHealthy(currentPort)) {
                            return;
                        }
                        
                        // 当前监听器监听到的串口返回数据
                        byte[] back = SerialPortManager.readFromPort(currentPort);
                        
                        if (back == null || back.length == 0) {
                            return;
                        }
                        
                        // 增加数据接收计数
                        dataReceivedCount++;

                        // 使用UTF-8编码转换字节数组为字符串
                        String receivedData = new String(back, java.nio.charset.StandardCharsets.UTF_8);

                        // 使用预编译的正则表达式提取数字部分
                        Matcher matcher = DATA_PATTERN.matcher(receivedData);
                        
                        if (matcher.find()) {
                            // 提取匹配的数字部分
                            String numericPart = matcher.group(1);
                            
                            // 转换为整数并更新原子变量
                            int parsedValue = Integer.parseInt(numericPart);
                            integerValue.set(parsedValue);
                            lastDataReceiveTime = System.currentTimeMillis(); // 更新数据接收时间
                            
                            // 只在值发生变化时打印日志
                            if (parsedValue != lastProcessedValue.get()) {
                                log.info("接收重量数据: {}g", parsedValue);
                            }
                        }
                        // 忽略不匹配的数据（如校验码等）
                    } catch (NumberFormatException e) {
                        log.error("数字解析失败: {}", e.getMessage());
                    } catch (Exception e) {
                        log.error("处理串口数据异常", e);
                    }
                }
                
                @Override
                public void onPortDisconnected() {
                    try {
                        log.error("监听器检测到串口断开事件，设置错误状态");
                        integerValue.set(STATUS_ERROR);
                        // 重置数据接收时间，避免重复触发超时检查
                        lastDataReceiveTime = 0;
                    } catch (Exception e) {
                        log.error("处理串口断开事件时发生异常", e);
                        // 确保即使异常也要设置错误状态
                        try {
                            integerValue.set(STATUS_ERROR);
                        } catch (Exception ex) {
                            log.error("设置错误状态失败", ex);
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("打开串口失败", e);
        }
    }

    /**
     * 应用关闭时清理串口资源
     */
    @PreDestroy
    public void cleanup() {
        log.info("应用关闭，开始清理串口资源");
        try {
            if (serialPort != null) {
                SerialPortManager.closePort(serialPort);
                log.info("串口资源清理完成");
            }
        } catch (Exception e) {
            log.error("清理串口资源失败", e);
        }
    }
}

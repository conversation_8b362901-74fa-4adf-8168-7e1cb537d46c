package com.xiaodu.serialportdemo.demos.web;

/**
 * @Author: zt
 * @CreateTime: 2025-01-18
 * @Version: 1.0
 */

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fazecast.jSerialComm.SerialPort;
import com.xiaodu.serialportdemo.demos.DataAvailableListener;
import com.xiaodu.serialportdemo.demos.SerialPortManager;
import com.xiaodu.serialportdemo.utils.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@RestController
@RequestMapping("/weight")
@Api(tags = "地磅接口")
//@CrossOrigin(origins = "*")
public class WeightController {

    // 状态常量
    private static final int STATUS_ERROR = -999999;      // 串口数据异常
    private static final int STATUS_NO_CHANGE = -888888;  // 载重信息无变化
    private static final BigDecimal SCALE_FACTOR = new BigDecimal(1000);

    @Autowired
    @Lazy
    private CarController controller;

    // 使用AtomicInteger保证线程安全
    private final AtomicInteger integerValue = new AtomicInteger(STATUS_ERROR);
    private final AtomicReference<BigDecimal> result = new AtomicReference<>(BigDecimal.ZERO);
    private SerialPort serialPort;

    /**
     * 获取重量
     */
    @GetMapping("/getWeight")
    @ApiOperation("获取地磅重量")
    public Result<BigDecimal> getWeight() {
        log.debug("开始获取载重变化数据");

        int currentValue = integerValue.get();

        if (currentValue == STATUS_ERROR) {
            log.debug("串口数据异常，尝试重新连接串口");
            reconnectSerialPort();
            return Result.build(null, Result.ResultCodeEnum.ERROR);
        }

        if (currentValue == STATUS_NO_CHANGE) {
            log.debug("载重信息无变化");
            return Result.ok(result.get());
        }

        // 将整数转换为BigDecimal并计算重量
        BigDecimal bigDecimalValue = new BigDecimal(currentValue);
        // 除以1000，得到小数，使用现代的RoundingMode
        BigDecimal calculatedResult = bigDecimalValue.divide(SCALE_FACTOR, 3, java.math.RoundingMode.HALF_UP);

        // 更新结果并标记为无变化状态
        result.set(calculatedResult);
        integerValue.set(STATUS_NO_CHANGE);

        log.debug("获取载重变化数据是：{}", calculatedResult);
        return Result.ok(calculatedResult);
    }

    private void reconnectSerialPort() {
        try {

            // 清理监听器
            if (serialPort != null && serialPort.isOpen()){
                serialPort.removeDataListener();
                log.info("清理旧监听器完成");
            }

            // 关闭串口
            SerialPortManager.closePort(serialPort);
            log.info("串口关闭完成");

            // 等待资源释放
            Thread.sleep(1000);
            log.info("资源释放等待完成");

            // 重新启动
            start();
            log.info("串口重新启动完成");
        }catch (InterruptedException e){
            log.error("串口重连等待被中断", e);
            Thread.currentThread().interrupt();
        }catch (Exception e){
            log.error("串口重连失败", e);
        }
    }

    @PostConstruct
    public void start() {
        try {
            JSONObject projectId = controller.getProjectId();
            if (projectId == null) {
                throw new RuntimeException("未获取到项目id");
            }
            JSONObject client = projectId.getJSONObject("client");
//        com=串口号 baudRate=波特率 dataBit=数据位 stopBit=停止位 verifyBit=校验位
            String com = client.getString("com");
            if (StringUtils.isBlank(com)) {
                com = "COM1";
            }
//            log.info("串口号：{}", com);
            String baudRateString = client.getString("baudRate");
            String dataBitString = client.getString("dataBit");
            String stopBitString = client.getString("stopBit");
            String verifyBitString = client.getString("verifyBit");
            if (baudRateString == null || baudRateString.isEmpty()) {
                baudRateString = "1200";
            }
            int baudRate = Integer.parseInt(baudRateString);
//            log.info("波特率：{}", baudRate);
            if (dataBitString == null || dataBitString.isEmpty()) {
                dataBitString = "8";
            }
            int dataBit = Integer.parseInt(dataBitString);
//            log.info("数据位：{}", dataBit);
            if (stopBitString == null || stopBitString.isEmpty()) {
                stopBitString = "1";
            }
            int stopBit = Integer.parseInt(stopBitString);
//            log.info("停止位：{}", stopBit);
            if (verifyBitString == null || verifyBitString.isEmpty()) {
                verifyBitString = "0";
            }
            int verifyBit = Integer.parseInt(verifyBitString);
            log.info("串口号：{};波特率：{};数据位：{};停止位：{};校验位：{}", com, baudRate, dataBit, stopBit, verifyBit);
//            SerialPort serialPort = SerialPortManager.openPort(com, baudRate, dataBit, verifyBit, stopBit);
            serialPort = SerialPortManager.openPort(com, baudRate, dataBit, verifyBit, stopBit);
            //给当前串口对象设置监听器
            SerialPortManager.addListener(serialPort, new DataAvailableListener() {
                @Override
                public void dataAvailable() {
                    // 当前监听器监听到的串口返回数据 back
                    byte[] back = SerialPortManager.readFromPort(serialPort);

                    // Convert byte array to string
                    String receivedData = new String(back);

                   log.info("接收到数据receivedData：{}", receivedData);

                    // 使用正则表达式提取数字部分
                    Pattern pattern = Pattern.compile("\\u0002([+-]?\\d{6})");
                    Matcher matcher = pattern.matcher(receivedData);
                    // 查找匹配项
                    if (matcher.find()) {
                        // 提取匹配的数字部分
                        String numericPart = matcher.group(1);

//                        log.info("接收到数据：{}", numericPart);

                        // 转换为整数并更新原子变量
                        int parsedValue = Integer.parseInt(numericPart);
                        integerValue.set(parsedValue);

                       log.info("接收整数值:{}", parsedValue);

                        // 打印整数值
                       System.out.println("接收整数值: " + parsedValue);
                    }
//                else {
//                    System.out.println("在接收到的数据中没有找到匹配项");
//                }
                }
            });
        } catch (Exception e) {
            log.error("打开串口失败", e);
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaodu.serialportdemo.mapper.WeighingDataMapper">
    <resultMap id="SQL" type="com.xiaodu.serialportdemo.vo.WeighingDataVo">
        <id column="id" property="id"/>
        <result column="createTime" property="createTime"/>
        <result column="weight" property="weight"/>
        <result column="carNumber" property="carNumber"/>
        <result column="dataType" property="dataType"/>
    </resultMap>
    <sql id="BaseSQL">
        tempdatapool.create_time as createTime,
        tempdatapool.weight as weight,
        tempdatapool.car_number as carNumber,
        tempdatapool.data_type as dataType,
        tempdatapool.head_image as headImage,
        tempdatapool.rear_image as rearImage,
        tempdatapool.is_pull as isPull
    </sql>
    <select id="getDataByCreateTime" resultType="com.xiaodu.serialportdemo.vo.WeighingDataVo">
        select tempdatapool.id AS id,
               tempdatapool.create_time AS createTime,
               tempdatapool.weight AS weight,
               tempdatapool.car_number AS carNumber,
               tempdatapool.data_type AS dataType,
               tempdatapool.is_pull as isPull
        from tempdatapool
        <where>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                tempdatapool.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        order by tempdatapool.create_time desc
        limit #{pageSize} offset #{offset}
    </select>
    <select id="getDataByCarNumber" resultType="com.xiaodu.serialportdemo.vo.WeighingDataVo">
        SELECT tempdatapool.id AS id,
               tempdatapool.create_time AS createTime,
               tempdatapool.weight AS weight,
               tempdatapool.car_number AS carNumber,
               tempdatapool.data_type AS dataType,
               tempdatapool.is_pull as isPull
        FROM tempdatapool
        <where>
            <if test="carNumber != null and carNumber != ''">
                tempdatapool.car_number LIKE #{carNumber} || '%'
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND tempdatapool.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        limit #{pageSize} offset #{offset}
    </select>

    <select id="getTotalCountByCarNumber" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tempdatapool
        <where>
            <if test="carNumber != null and carNumber != ''">
                tempdatapool.car_number LIKE #{carNumber} || '%'
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND tempdatapool.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="getTotalCountByCreateTime" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tempdatapool
        <where>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                tempdatapool.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="selectDataByIsPull" resultType="com.xiaodu.serialportdemo.dto.TempDataPoolDo"
            parameterType="int">
        SELECT * FROM tempdatapool WHERE is_pull = '2'
    </select>
    <update id="updateAll" parameterType="java.util.List">
        <foreach collection="tempDataPoolDoList" item="item" separator=";">
            UPDATE tempdatapool
            SET is_pull = #{item.isPull}
            WHERE id = #{item.id}
        </foreach>
    </update>
    <select id="getImageById" resultType="com.xiaodu.serialportdemo.vo.WeighingDataVo"
            parameterType="java.lang.String">
        SELECT tempdatapool.head_image as headImage,
               tempdatapool.rear_image as rearImage
        FROM tempdatapool
        WHERE id = #{id}
    </select>
</mapper>

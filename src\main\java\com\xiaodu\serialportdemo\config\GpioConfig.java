package com.xiaodu.serialportdemo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * GPIO道闸控制配置类
 * 专为8_D6直流无刷道闸主板设计，支持全参数配置
 * 从application.yml读取gpio相关配置，包含：
 * - 基础GPIO控制参数
 * - 道闸运行参数(速度/力度/时间)
 * - 安全保护参数(障碍检测/反转/限力)
 * - 信号控制参数(脉冲/电压/电流)
 * - 状态监控参数(反馈/上报间隔)
 * - 环境适配参数(温度/湿度/防护等级)
 * - 维护参数(润滑/报警/提醒)
 */
@Data
@Component
@ConfigurationProperties(prefix = "gpio")
public class GpioConfig {

    /**
     * GPIO信号延时(毫秒)
     */
    private int delay = 500;

    /**
     * GPIO输出值，1=高电平，0=低电平
     */
    private int outputValue = 1;

    /**
     * GPIO操作超时时间(毫秒)
     */
    private int timeout = 3000;

    /**
     * 失败重试次数
     */
    private int retryCount = 3;

    /**
     * IO端口映射配置
     */
    private IoMapping ioMapping = new IoMapping();
    
    /**
     * 道闸运行参数配置
     */
    private Barrier barrier = new Barrier();
    
    /**
     * 安全保护参数配置
     */
    private Safety safety = new Safety();
    
    /**
     * 信号控制参数配置
     */
    private Signal signal = new Signal();
    
    /**
     * 状态监控参数配置
     */
    private Monitor monitor = new Monitor();
    
    /**
     * 环境适配参数配置
     */
    private Environment environment = new Environment();
    
    /**
     * 维护参数配置
     */
    private Maintenance maintenance = new Maintenance();

    @Data
    public static class IoMapping {
        /**
         * 抬杆IO端口
         */
        private int raise = 0;

        /**
         * 落杆IO端口
         */
        private int drop = 1;
    }
    
    @Data
    public static class Barrier {
        /**
         * 抬杆完成时间(毫秒)
         */
        private int raiseTime = 3000;
        
        /**
         * 落杆完成时间(毫秒)
         */
        private int dropTime = 2000;
        
        /**
         * 最大保持时间(毫秒)
         */
        private int holdTime = 30000;
        
        /**
         * 力度等级 1-3 (1=轻, 2=中, 3=重)
         */
        private int forceLevel = 2;
        
        /**
         * 速度等级 1-3 (1=慢, 2=中, 3=快)
         */
        private int speedLevel = 2;
    }
    
    @Data
    public static class Safety {
        /**
         * 启用障碍物检测
         */
        private boolean obstacleDetect = true;
        
        /**
         * 遇阻自动反转
         */
        private boolean autoReverse = true;
        
        /**
         * 紧急停止功能
         */
        private boolean emergencyStop = true;
        
        /**
         * 最大输出力度(%)
         */
        private int maxForce = 80;
        
        /**
         * 压力保护阈值(%)
         */
        private int pressureLimit = 50;
    }
    
    @Data
    public static class Signal {
        /**
         * 脉冲宽度(毫秒)
         */
        private int pulseWidth = 200;
        
        /**
         * 脉冲间隔(毫秒)
         */
        private int pulseInterval = 100;
        
        /**
         * 信号电压(V): 12V/24V
         */
        private int signalLevel = 24;
        
        /**
         * 驱动电流(mA)
         */
        private int driveCurrent = 500;
        
        /**
         * 启用电磁制动
         */
        private boolean brakeEnable = true;
    }
    
    @Data
    public static class Monitor {
        /**
         * 位置反馈
         */
        private boolean positionFeedback = true;
        
        /**
         * 电流监控
         */
        private boolean currentMonitor = true;
        
        /**
         * 温度监控
         */
        private boolean temperatureMonitor = true;
        
        /**
         * 状态上报间隔(毫秒)
         */
        private int statusReportInterval = 1000;
        
        /**
         * 心跳间隔(毫秒)
         */
        private int heartbeatInterval = 5000;
    }
    
    @Data
    public static class Environment {
        /**
         * 工作温度范围(°C)
         */
        private String temperatureRange = "-20~60";
        
        /**
         * 最大湿度(%)
         */
        private int humidityMax = 85;
        
        /**
         * 抗风等级
         */
        private int windResistance = 6;
        
        /**
         * 防护等级
         */
        private String waterproofLevel = "IP54";
    }
    
    @Data
    public static class Maintenance {
        /**
         * 自动润滑
         */
        private boolean autoLubrication = false;
        
        /**
         * 循环次数报警阈值
         */
        private int cycleCountAlarm = 100000;
        
        /**
         * 维护提醒周期(天)
         */
        private int maintenanceReminder = 30;
        
        /**
         * 错误代码上报
         */
        private boolean errorCodeReport = true;
    }
}
